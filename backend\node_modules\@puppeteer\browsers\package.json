{"name": "@puppeteer/browsers", "version": "1.4.6", "description": "Download and launch browsers", "scripts": {"build:docs": "wireit", "build": "wireit", "build:test": "wireit", "clean": "tsc --build --clean && rm -rf lib", "test": "wireit"}, "bin": "lib/cjs/main-cli.js", "main": "./lib/cjs/main.js", "module": "./lib/esm/main.js", "type": "commonjs", "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "wireit": {"build": {"command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "files": ["src/**/*.ts", "tsconfig.json"], "clean": "if-file-deleted", "output": ["lib/**", "!lib/esm/package.json"], "dependencies": ["generate:package-json"]}, "generate:package-json": {"command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json", "files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"]}, "build:docs": {"command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "dependencies": ["build"]}, "build:test": {"command": "tsc -b test/src/tsconfig.json", "files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "dependencies": ["build", "../testserver:build"]}, "test": {"command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "files": [".mocharc.cjs"], "dependencies": ["build:test"]}}, "keywords": ["puppeteer", "browsers"], "repository": {"type": "git", "url": "https://github.com/puppeteer/puppeteer/tree/main/packages/browsers"}, "author": "The Chromium Authors", "license": "Apache-2.0", "engines": {"node": ">=16.3.0"}, "files": ["lib", "src", "!*.tsbuil<PERSON><PERSON>"], "dependencies": {"debug": "4.3.4", "extract-zip": "2.0.1", "progress": "2.0.3", "proxy-agent": "6.3.0", "tar-fs": "3.0.4", "unbzip2-stream": "1.4.3", "yargs": "17.7.1"}, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}