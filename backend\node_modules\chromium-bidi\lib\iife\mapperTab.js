var mapperTab=function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t={},r={},n={},s={},a={},i={},o={};!function(e){var t;Object.defineProperty(e,"__esModule",{value:!0}),e.getParsedType=e.ZodParsedType=e.objectUtil=e.util=void 0,function(e){e.assertEqual=e=>e,e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const r of e)t[r]=r;return t},e.getValidEnumValues=t=>{const r=e.objectKeys(t).filter((e=>"number"!=typeof t[t[e]])),n={};for(const e of r)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map((function(e){return t[e]})),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(const r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map((e=>"string"==typeof e?`'${e}'`:e)).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(t=e.util||(e.util={})),(e.objectUtil||(e.objectUtil={})).mergeShapes=(e,t)=>({...e,...t}),e.ZodParsedType=t.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]);e.getParsedType=t=>{switch(typeof t){case"undefined":return e.ZodParsedType.undefined;case"string":return e.ZodParsedType.string;case"number":return isNaN(t)?e.ZodParsedType.nan:e.ZodParsedType.number;case"boolean":return e.ZodParsedType.boolean;case"function":return e.ZodParsedType.function;case"bigint":return e.ZodParsedType.bigint;case"symbol":return e.ZodParsedType.symbol;case"object":return Array.isArray(t)?e.ZodParsedType.array:null===t?e.ZodParsedType.null:t.then&&"function"==typeof t.then&&t.catch&&"function"==typeof t.catch?e.ZodParsedType.promise:"undefined"!=typeof Map&&t instanceof Map?e.ZodParsedType.map:"undefined"!=typeof Set&&t instanceof Set?e.ZodParsedType.set:"undefined"!=typeof Date&&t instanceof Date?e.ZodParsedType.date:e.ZodParsedType.object;default:return e.ZodParsedType.unknown}}}(o);var d={};Object.defineProperty(d,"__esModule",{value:!0}),d.ZodError=d.quotelessJson=d.ZodIssueCode=void 0;const c=o;d.ZodIssueCode=c.util.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);d.quotelessJson=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class u extends Error{constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}get errors(){return this.issues}format(e){const t=e||function(e){return e.message},r={_errors:[]},n=e=>{for(const s of e.issues)if("invalid_union"===s.code)s.unionErrors.map(n);else if("invalid_return_type"===s.code)n(s.returnTypeError);else if("invalid_arguments"===s.code)n(s.argumentsError);else if(0===s.path.length)r._errors.push(t(s));else{let e=r,n=0;for(;n<s.path.length;){const r=s.path[n];n===s.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(s))):e[r]=e[r]||{_errors:[]},e=e[r],n++}}};return n(this),r}toString(){return this.message}get message(){return JSON.stringify(this.issues,c.util.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=(e=>e.message)){const t={},r=[];for(const n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}d.ZodError=u,u.create=e=>new u(e),Object.defineProperty(i,"__esModule",{value:!0});const l=o,p=d;i.default=(e,t)=>{let r;switch(e.code){case p.ZodIssueCode.invalid_type:r=e.received===l.ZodParsedType.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case p.ZodIssueCode.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,l.util.jsonStringifyReplacer)}`;break;case p.ZodIssueCode.unrecognized_keys:r=`Unrecognized key(s) in object: ${l.util.joinValues(e.keys,", ")}`;break;case p.ZodIssueCode.invalid_union:r="Invalid input";break;case p.ZodIssueCode.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${l.util.joinValues(e.options)}`;break;case p.ZodIssueCode.invalid_enum_value:r=`Invalid enum value. Expected ${l.util.joinValues(e.options)}, received '${e.received}'`;break;case p.ZodIssueCode.invalid_arguments:r="Invalid function arguments";break;case p.ZodIssueCode.invalid_return_type:r="Invalid function return type";break;case p.ZodIssueCode.invalid_date:r="Invalid date";break;case p.ZodIssueCode.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:l.util.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case p.ZodIssueCode.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case p.ZodIssueCode.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case p.ZodIssueCode.custom:r="Invalid input";break;case p.ZodIssueCode.invalid_intersection_types:r="Intersection results could not be merged";break;case p.ZodIssueCode.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case p.ZodIssueCode.not_finite:r="Number must be finite";break;default:r=t.defaultError,l.util.assertNever(e)}return{message:r}};var h=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(a,"__esModule",{value:!0}),a.getErrorMap=a.setErrorMap=a.defaultErrorMap=void 0;const m=h(i);a.defaultErrorMap=m.default;let g=m.default;a.setErrorMap=function(e){g=e},a.getErrorMap=function(){return g};var f={};!function(t){var r=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.isAsync=t.isValid=t.isDirty=t.isAborted=t.OK=t.DIRTY=t.INVALID=t.ParseStatus=t.addIssueToContext=t.EMPTY_PATH=t.makeIssue=void 0;const n=a,s=r(i);t.makeIssue=e=>{const{data:t,path:r,errorMaps:n,issueData:s}=e,a=[...r,...s.path||[]],i={...s,path:a};let o="";const d=n.filter((e=>!!e)).slice().reverse();for(const e of d)o=e(i,{data:t,defaultError:o}).message;return{...s,path:a,message:s.message||o}},t.EMPTY_PATH=[],t.addIssueToContext=function(e,r){const a=(0,t.makeIssue)({issueData:r,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,(0,n.getErrorMap)(),s.default].filter((e=>!!e))});e.common.issues.push(a)};class o{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,r){const n=[];for(const s of r){if("aborted"===s.status)return t.INVALID;"dirty"===s.status&&e.dirty(),n.push(s.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const r=[];for(const e of t)r.push({key:await e.key,value:await e.value});return o.mergeObjectSync(e,r)}static mergeObjectSync(e,r){const n={};for(const s of r){const{key:r,value:a}=s;if("aborted"===r.status)return t.INVALID;if("aborted"===a.status)return t.INVALID;"dirty"===r.status&&e.dirty(),"dirty"===a.status&&e.dirty(),(void 0!==a.value||s.alwaysSet)&&(n[r.value]=a.value)}return{status:e.value,value:n}}}t.ParseStatus=o,t.INVALID=Object.freeze({status:"aborted"});t.DIRTY=e=>({status:"dirty",value:e});t.OK=e=>({status:"valid",value:e});t.isAborted=e=>"aborted"===e.status;t.isDirty=e=>"dirty"===e.status;t.isValid=e=>"valid"===e.status;t.isAsync=e=>"undefined"!=typeof Promise&&e instanceof Promise}(f);var v={};Object.defineProperty(v,"__esModule",{value:!0});var y,x={},w={};y=w,Object.defineProperty(y,"__esModule",{value:!0}),y.errorUtil=void 0,function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:null==e?void 0:e.message}(y.errorUtil||(y.errorUtil={})),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.discriminatedUnion=e.date=e.boolean=e.bigint=e.array=e.any=e.coerce=e.ZodFirstPartyTypeKind=e.late=e.ZodSchema=e.Schema=e.custom=e.ZodPipeline=e.ZodBranded=e.BRAND=e.ZodNaN=e.ZodCatch=e.ZodDefault=e.ZodNullable=e.ZodOptional=e.ZodTransformer=e.ZodEffects=e.ZodPromise=e.ZodNativeEnum=e.ZodEnum=e.ZodLiteral=e.ZodLazy=e.ZodFunction=e.ZodSet=e.ZodMap=e.ZodRecord=e.ZodTuple=e.ZodIntersection=e.ZodDiscriminatedUnion=e.ZodUnion=e.ZodObject=e.ZodArray=e.ZodVoid=e.ZodNever=e.ZodUnknown=e.ZodAny=e.ZodNull=e.ZodUndefined=e.ZodSymbol=e.ZodDate=e.ZodBoolean=e.ZodBigInt=e.ZodNumber=e.ZodString=e.ZodType=void 0,e.NEVER=e.void=e.unknown=e.union=e.undefined=e.tuple=e.transformer=e.symbol=e.string=e.strictObject=e.set=e.record=e.promise=e.preprocess=e.pipeline=e.ostring=e.optional=e.onumber=e.oboolean=e.object=e.number=e.nullable=e.null=e.never=e.nativeEnum=e.nan=e.map=e.literal=e.lazy=e.intersection=e.instanceof=e.function=e.enum=e.effect=void 0;const t=a,r=w,n=f,s=o,i=d;class c{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const u=(e,t)=>{if((0,n.isValid)(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new i.ZodError(e.common.issues);return this._error=t,this._error}}};function l(e){if(!e)return{};const{errorMap:t,invalid_type_error:r,required_error:n,description:s}=e;if(t&&(r||n))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:s};return{errorMap:(e,t)=>"invalid_type"!==e.code?{message:t.defaultError}:void 0===t.data?{message:null!=n?n:t.defaultError}:{message:null!=r?r:t.defaultError},description:s}}class p{constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this)}get description(){return this._def.description}_getType(e){return(0,s.getParsedType)(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:(0,s.getParsedType)(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new n.ParseStatus,ctx:{common:e.parent.common,data:e.data,parsedType:(0,s.getParsedType)(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if((0,n.isAsync)(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){var r;const n={common:{issues:[],async:null!==(r=null==t?void 0:t.async)&&void 0!==r&&r,contextualErrorMap:null==t?void 0:t.errorMap},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:(0,s.getParsedType)(e)},a=this._parseSync({data:e,path:n.path,parent:n});return u(n,a)}async parseAsync(e,t){const r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){const r={common:{issues:[],contextualErrorMap:null==t?void 0:t.errorMap,async:!0},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:(0,s.getParsedType)(e)},a=this._parse({data:e,path:r.path,parent:r}),i=await((0,n.isAsync)(a)?a:Promise.resolve(a));return u(r,i)}refine(e,t){const r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement(((t,n)=>{const s=e(t),a=()=>n.addIssue({code:i.ZodIssueCode.custom,...r(t)});return"undefined"!=typeof Promise&&s instanceof Promise?s.then((e=>!!e||(a(),!1))):!!s||(a(),!1)}))}refinement(e,t){return this._refinement(((r,n)=>!!e(r)||(n.addIssue("function"==typeof t?t(r,n):t),!1)))}_refinement(e){return new te({schema:this,typeName:ce.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}optional(){return re.create(this,this._def)}nullable(){return ne.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return z.create(this,this._def)}promise(){return ee.create(this,this._def)}or(e){return L.create([this,e],this._def)}and(e){return U.create(this,e,this._def)}transform(e){return new te({...l(this._def),schema:this,typeName:ce.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new se({...l(this._def),innerType:this,defaultValue:t,typeName:ce.ZodDefault})}brand(){return new oe({typeName:ce.ZodBranded,type:this,...l(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new ae({...l(this._def),innerType:this,catchValue:t,typeName:ce.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return de.create(this,e)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}e.ZodType=p,e.Schema=p,e.ZodSchema=p;const h=/^c[^\s-]{8,}$/i,m=/^[a-z][a-z0-9]*$/,g=/[0-9A-HJKMNP-TV-Z]{26}/,v=/^([a-f0-9]{8}-[a-f0-9]{4}-[1-5][a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}|00000000-0000-0000-0000-000000000000)$/i,y=/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\])|(\[IPv6:(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))\])|([A-Za-z0-9]([A-Za-z0-9-]*[A-Za-z0-9])*(\.[A-Za-z]{2,})+))$/,x=/^(\p{Extended_Pictographic}|\p{Emoji_Component})+$/u,b=/^(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))$/,C=/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/;class I extends p{constructor(){super(...arguments),this._regex=(e,t,n)=>this.refinement((t=>e.test(t)),{validation:t,code:i.ZodIssueCode.invalid_string,...r.errorUtil.errToObj(n)}),this.nonempty=e=>this.min(1,r.errorUtil.errToObj(e)),this.trim=()=>new I({...this._def,checks:[...this._def.checks,{kind:"trim"}]}),this.toLowerCase=()=>new I({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]}),this.toUpperCase=()=>new I({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}_parse(e){this._def.coerce&&(e.data=String(e.data));if(this._getType(e)!==s.ZodParsedType.string){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.string,received:t.parsedType}),n.INVALID}const t=new n.ParseStatus;let r;for(const c of this._def.checks)if("min"===c.kind)e.data.length<c.value&&(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,minimum:c.value,type:"string",inclusive:!0,exact:!1,message:c.message}),t.dirty());else if("max"===c.kind)e.data.length>c.value&&(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,maximum:c.value,type:"string",inclusive:!0,exact:!1,message:c.message}),t.dirty());else if("length"===c.kind){const s=e.data.length>c.value,a=e.data.length<c.value;(s||a)&&(r=this._getOrReturnCtx(e,r),s?(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,maximum:c.value,type:"string",inclusive:!0,exact:!0,message:c.message}):a&&(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,minimum:c.value,type:"string",inclusive:!0,exact:!0,message:c.message}),t.dirty())}else if("email"===c.kind)y.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{validation:"email",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("emoji"===c.kind)x.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{validation:"emoji",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("uuid"===c.kind)v.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{validation:"uuid",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("cuid"===c.kind)h.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{validation:"cuid",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("cuid2"===c.kind)m.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{validation:"cuid2",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("ulid"===c.kind)g.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{validation:"ulid",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("url"===c.kind)try{new URL(e.data)}catch(s){r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{validation:"url",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty()}else if("regex"===c.kind){c.regex.lastIndex=0;c.regex.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{validation:"regex",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty())}else if("trim"===c.kind)e.data=e.data.trim();else if("includes"===c.kind)e.data.includes(c.value,c.position)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_string,validation:{includes:c.value,position:c.position},message:c.message}),t.dirty());else if("toLowerCase"===c.kind)e.data=e.data.toLowerCase();else if("toUpperCase"===c.kind)e.data=e.data.toUpperCase();else if("startsWith"===c.kind)e.data.startsWith(c.value)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_string,validation:{startsWith:c.value},message:c.message}),t.dirty());else if("endsWith"===c.kind)e.data.endsWith(c.value)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_string,validation:{endsWith:c.value},message:c.message}),t.dirty());else if("datetime"===c.kind){((d=c).precision?d.offset?new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${d.precision}}(([+-]\\d{2}(:?\\d{2})?)|Z)$`):new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${d.precision}}Z$`):0===d.precision?d.offset?new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(([+-]\\d{2}(:?\\d{2})?)|Z)$"):new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$"):d.offset?new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?(([+-]\\d{2}(:?\\d{2})?)|Z)$"):new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$")).test(e.data)||(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_string,validation:"datetime",message:c.message}),t.dirty())}else"ip"===c.kind?(a=e.data,("v4"!==(o=c.version)&&o||!b.test(a))&&("v6"!==o&&o||!C.test(a))&&(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{validation:"ip",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty())):s.util.assertNever(c);var a,o,d;return{status:t.value,value:e.data}}_addCheck(e){return new I({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...r.errorUtil.errToObj(e)})}url(e){return this._addCheck({kind:"url",...r.errorUtil.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...r.errorUtil.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...r.errorUtil.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...r.errorUtil.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...r.errorUtil.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...r.errorUtil.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...r.errorUtil.errToObj(e)})}datetime(e){var t;return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t&&t,...r.errorUtil.errToObj(null==e?void 0:e.message)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...r.errorUtil.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:null==t?void 0:t.position,...r.errorUtil.errToObj(null==t?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...r.errorUtil.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...r.errorUtil.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...r.errorUtil.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...r.errorUtil.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...r.errorUtil.errToObj(t)})}get isDatetime(){return!!this._def.checks.find((e=>"datetime"===e.kind))}get isEmail(){return!!this._def.checks.find((e=>"email"===e.kind))}get isURL(){return!!this._def.checks.find((e=>"url"===e.kind))}get isEmoji(){return!!this._def.checks.find((e=>"emoji"===e.kind))}get isUUID(){return!!this._def.checks.find((e=>"uuid"===e.kind))}get isCUID(){return!!this._def.checks.find((e=>"cuid"===e.kind))}get isCUID2(){return!!this._def.checks.find((e=>"cuid2"===e.kind))}get isULID(){return!!this._def.checks.find((e=>"ulid"===e.kind))}get isIP(){return!!this._def.checks.find((e=>"ip"===e.kind))}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function _(e,t){const r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,s=r>n?r:n;return parseInt(e.toFixed(s).replace(".",""))%parseInt(t.toFixed(s).replace(".",""))/Math.pow(10,s)}e.ZodString=I,I.create=e=>{var t;return new I({checks:[],typeName:ce.ZodString,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...l(e)})};class S extends p{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));if(this._getType(e)!==s.ZodParsedType.number){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.number,received:t.parsedType}),n.INVALID}let t;const r=new n.ParseStatus;for(const a of this._def.checks)if("int"===a.kind)s.util.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty());else if("min"===a.kind){(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty())}else if("max"===a.kind){(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty())}else"multipleOf"===a.kind?0!==_(e.data,a.value)&&(t=this._getOrReturnCtx(e,t),(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):"finite"===a.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.not_finite,message:a.message}),r.dirty()):s.util.assertNever(a);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,r.errorUtil.toString(t))}gt(e,t){return this.setLimit("min",e,!1,r.errorUtil.toString(t))}lte(e,t){return this.setLimit("max",e,!0,r.errorUtil.toString(t))}lt(e,t){return this.setLimit("max",e,!1,r.errorUtil.toString(t))}setLimit(e,t,n,s){return new S({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:r.errorUtil.toString(s)}]})}_addCheck(e){return new S({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:r.errorUtil.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:r.errorUtil.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:r.errorUtil.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:r.errorUtil.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:r.errorUtil.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:r.errorUtil.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:r.errorUtil.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:r.errorUtil.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:r.errorUtil.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find((e=>"int"===e.kind||"multipleOf"===e.kind&&s.util.isInteger(e.value)))}get isFinite(){let e=null,t=null;for(const r of this._def.checks){if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value)}return Number.isFinite(t)&&Number.isFinite(e)}}e.ZodNumber=S,S.create=e=>new S({checks:[],typeName:ce.ZodNumber,coerce:(null==e?void 0:e.coerce)||!1,...l(e)});class T extends p{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){this._def.coerce&&(e.data=BigInt(e.data));if(this._getType(e)!==s.ZodParsedType.bigint){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.bigint,received:t.parsedType}),n.INVALID}let t;const r=new n.ParseStatus;for(const a of this._def.checks)if("min"===a.kind){(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty())}else if("max"===a.kind){(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty())}else"multipleOf"===a.kind?e.data%a.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):s.util.assertNever(a);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,r.errorUtil.toString(t))}gt(e,t){return this.setLimit("min",e,!1,r.errorUtil.toString(t))}lte(e,t){return this.setLimit("max",e,!0,r.errorUtil.toString(t))}lt(e,t){return this.setLimit("max",e,!1,r.errorUtil.toString(t))}setLimit(e,t,n,s){return new T({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:r.errorUtil.toString(s)}]})}_addCheck(e){return new T({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:r.errorUtil.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:r.errorUtil.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:r.errorUtil.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:r.errorUtil.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:r.errorUtil.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}e.ZodBigInt=T,T.create=e=>{var t;return new T({checks:[],typeName:ce.ZodBigInt,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...l(e)})};class P extends p{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==s.ZodParsedType.boolean){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.boolean,received:t.parsedType}),n.INVALID}return(0,n.OK)(e.data)}}e.ZodBoolean=P,P.create=e=>new P({typeName:ce.ZodBoolean,coerce:(null==e?void 0:e.coerce)||!1,...l(e)});class E extends p{_parse(e){this._def.coerce&&(e.data=new Date(e.data));if(this._getType(e)!==s.ZodParsedType.date){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.date,received:t.parsedType}),n.INVALID}if(isNaN(e.data.getTime())){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_date}),n.INVALID}const t=new n.ParseStatus;let r;for(const a of this._def.checks)"min"===a.kind?e.data.getTime()<a.value&&(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),t.dirty()):"max"===a.kind?e.data.getTime()>a.value&&(r=this._getOrReturnCtx(e,r),(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),t.dirty()):s.util.assertNever(a);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new E({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:r.errorUtil.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:r.errorUtil.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}e.ZodDate=E,E.create=e=>new E({checks:[],coerce:(null==e?void 0:e.coerce)||!1,typeName:ce.ZodDate,...l(e)});class k extends p{_parse(e){if(this._getType(e)!==s.ZodParsedType.symbol){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.symbol,received:t.parsedType}),n.INVALID}return(0,n.OK)(e.data)}}e.ZodSymbol=k,k.create=e=>new k({typeName:ce.ZodSymbol,...l(e)});class N extends p{_parse(e){if(this._getType(e)!==s.ZodParsedType.undefined){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.undefined,received:t.parsedType}),n.INVALID}return(0,n.OK)(e.data)}}e.ZodUndefined=N,N.create=e=>new N({typeName:ce.ZodUndefined,...l(e)});class M extends p{_parse(e){if(this._getType(e)!==s.ZodParsedType.null){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.null,received:t.parsedType}),n.INVALID}return(0,n.OK)(e.data)}}e.ZodNull=M,M.create=e=>new M({typeName:ce.ZodNull,...l(e)});class O extends p{constructor(){super(...arguments),this._any=!0}_parse(e){return(0,n.OK)(e.data)}}e.ZodAny=O,O.create=e=>new O({typeName:ce.ZodAny,...l(e)});class D extends p{constructor(){super(...arguments),this._unknown=!0}_parse(e){return(0,n.OK)(e.data)}}e.ZodUnknown=D,D.create=e=>new D({typeName:ce.ZodUnknown,...l(e)});class Z extends p{_parse(e){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.never,received:t.parsedType}),n.INVALID}}e.ZodNever=Z,Z.create=e=>new Z({typeName:ce.ZodNever,...l(e)});class R extends p{_parse(e){if(this._getType(e)!==s.ZodParsedType.undefined){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.void,received:t.parsedType}),n.INVALID}return(0,n.OK)(e.data)}}e.ZodVoid=R,R.create=e=>new R({typeName:ce.ZodVoid,...l(e)});class z extends p{_parse(e){const{ctx:t,status:r}=this._processInputParams(e),a=this._def;if(t.parsedType!==s.ZodParsedType.array)return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.array,received:t.parsedType}),n.INVALID;if(null!==a.exactLength){const e=t.data.length>a.exactLength.value,s=t.data.length<a.exactLength.value;(e||s)&&((0,n.addIssueToContext)(t,{code:e?i.ZodIssueCode.too_big:i.ZodIssueCode.too_small,minimum:s?a.exactLength.value:void 0,maximum:e?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),r.dirty())}if(null!==a.minLength&&t.data.length<a.minLength.value&&((0,n.addIssueToContext)(t,{code:i.ZodIssueCode.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),r.dirty()),null!==a.maxLength&&t.data.length>a.maxLength.value&&((0,n.addIssueToContext)(t,{code:i.ZodIssueCode.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map(((e,r)=>a.type._parseAsync(new c(t,e,t.path,r))))).then((e=>n.ParseStatus.mergeArray(r,e)));const o=[...t.data].map(((e,r)=>a.type._parseSync(new c(t,e,t.path,r))));return n.ParseStatus.mergeArray(r,o)}get element(){return this._def.type}min(e,t){return new z({...this._def,minLength:{value:e,message:r.errorUtil.toString(t)}})}max(e,t){return new z({...this._def,maxLength:{value:e,message:r.errorUtil.toString(t)}})}length(e,t){return new z({...this._def,exactLength:{value:e,message:r.errorUtil.toString(t)}})}nonempty(e){return this.min(1,e)}}function A(e){if(e instanceof j){const t={};for(const r in e.shape){const n=e.shape[r];t[r]=re.create(A(n))}return new j({...e._def,shape:()=>t})}return e instanceof z?new z({...e._def,type:A(e.element)}):e instanceof re?re.create(A(e.unwrap())):e instanceof ne?ne.create(A(e.unwrap())):e instanceof V?V.create(e.items.map((e=>A(e)))):e}e.ZodArray=z,z.create=(e,t)=>new z({type:e,minLength:null,maxLength:null,exactLength:null,typeName:ce.ZodArray,...l(t)});class j extends p{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=s.util.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==s.ZodParsedType.object){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.object,received:t.parsedType}),n.INVALID}const{status:t,ctx:r}=this._processInputParams(e),{shape:a,keys:o}=this._getCached(),d=[];if(!(this._def.catchall instanceof Z&&"strip"===this._def.unknownKeys))for(const e in r.data)o.includes(e)||d.push(e);const u=[];for(const e of o){const t=a[e],n=r.data[e];u.push({key:{status:"valid",value:e},value:t._parse(new c(r,n,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof Z){const e=this._def.unknownKeys;if("passthrough"===e)for(const e of d)u.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)d.length>0&&((0,n.addIssueToContext)(r,{code:i.ZodIssueCode.unrecognized_keys,keys:d}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of d){const n=r.data[t];u.push({key:{status:"valid",value:t},value:e._parse(new c(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then((async()=>{const e=[];for(const t of u){const r=await t.key;e.push({key:r,value:await t.value,alwaysSet:t.alwaysSet})}return e})).then((e=>n.ParseStatus.mergeObjectSync(t,e))):n.ParseStatus.mergeObjectSync(t,u)}get shape(){return this._def.shape()}strict(e){return r.errorUtil.errToObj,new j({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,n)=>{var s,a,i,o;const d=null!==(i=null===(a=(s=this._def).errorMap)||void 0===a?void 0:a.call(s,t,n).message)&&void 0!==i?i:n.defaultError;return"unrecognized_keys"===t.code?{message:null!==(o=r.errorUtil.errToObj(e).message)&&void 0!==o?o:d}:{message:d}}}:{}})}strip(){return new j({...this._def,unknownKeys:"strip"})}passthrough(){return new j({...this._def,unknownKeys:"passthrough"})}extend(e){return new j({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new j({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:ce.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new j({...this._def,catchall:e})}pick(e){const t={};return s.util.objectKeys(e).forEach((r=>{e[r]&&this.shape[r]&&(t[r]=this.shape[r])})),new j({...this._def,shape:()=>t})}omit(e){const t={};return s.util.objectKeys(this.shape).forEach((r=>{e[r]||(t[r]=this.shape[r])})),new j({...this._def,shape:()=>t})}deepPartial(){return A(this)}partial(e){const t={};return s.util.objectKeys(this.shape).forEach((r=>{const n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()})),new j({...this._def,shape:()=>t})}required(e){const t={};return s.util.objectKeys(this.shape).forEach((r=>{if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof re;)e=e._def.innerType;t[r]=e}})),new j({...this._def,shape:()=>t})}keyof(){return X(s.util.objectKeys(this.shape))}}e.ZodObject=j,j.create=(e,t)=>new j({shape:()=>e,unknownKeys:"strip",catchall:Z.create(),typeName:ce.ZodObject,...l(t)}),j.strictCreate=(e,t)=>new j({shape:()=>e,unknownKeys:"strict",catchall:Z.create(),typeName:ce.ZodObject,...l(t)}),j.lazycreate=(e,t)=>new j({shape:e,unknownKeys:"strip",catchall:Z.create(),typeName:ce.ZodObject,...l(t)});class L extends p{_parse(e){const{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map((async e=>{const r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}}))).then((function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;const r=e.map((e=>new i.ZodError(e.ctx.common.issues)));return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_union,unionErrors:r}),n.INVALID}));{let e;const s=[];for(const n of r){const r={...t,common:{...t.common,issues:[]},parent:null},a=n._parseSync({data:t.data,path:t.path,parent:r});if("valid"===a.status)return a;"dirty"!==a.status||e||(e={result:a,ctx:r}),r.common.issues.length&&s.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const a=s.map((e=>new i.ZodError(e)));return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_union,unionErrors:a}),n.INVALID}}get options(){return this._def.options}}e.ZodUnion=L,L.create=(e,t)=>new L({options:e,typeName:ce.ZodUnion,...l(t)});const B=e=>e instanceof J?B(e.schema):e instanceof te?B(e.innerType()):e instanceof G?[e.value]:e instanceof Y?e.options:e instanceof Q?Object.keys(e.enum):e instanceof se?B(e._def.innerType):e instanceof N?[void 0]:e instanceof M?[null]:null;class K extends p{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==s.ZodParsedType.object)return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.object,received:t.parsedType}),n.INVALID;const r=this.discriminator,a=t.data[r],o=this.optionsMap.get(a);return o?t.common.async?o._parseAsync({data:t.data,path:t.path,parent:t}):o._parseSync({data:t.data,path:t.path,parent:t}):((0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),n.INVALID)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){const n=new Map;for(const r of t){const t=B(r.shape[e]);if(!t)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const s of t){if(n.has(s))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);n.set(s,r)}}return new K({typeName:ce.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...l(r)})}}function F(e,t){const r=(0,s.getParsedType)(e),n=(0,s.getParsedType)(t);if(e===t)return{valid:!0,data:e};if(r===s.ZodParsedType.object&&n===s.ZodParsedType.object){const r=s.util.objectKeys(t),n=s.util.objectKeys(e).filter((e=>-1!==r.indexOf(e))),a={...e,...t};for(const r of n){const n=F(e[r],t[r]);if(!n.valid)return{valid:!1};a[r]=n.data}return{valid:!0,data:a}}if(r===s.ZodParsedType.array&&n===s.ZodParsedType.array){if(e.length!==t.length)return{valid:!1};const r=[];for(let n=0;n<e.length;n++){const s=F(e[n],t[n]);if(!s.valid)return{valid:!1};r.push(s.data)}return{valid:!0,data:r}}return r===s.ZodParsedType.date&&n===s.ZodParsedType.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}e.ZodDiscriminatedUnion=K;class U extends p{_parse(e){const{status:t,ctx:r}=this._processInputParams(e),s=(e,s)=>{if((0,n.isAborted)(e)||(0,n.isAborted)(s))return n.INVALID;const a=F(e.value,s.value);return a.valid?(((0,n.isDirty)(e)||(0,n.isDirty)(s))&&t.dirty(),{status:t.value,value:a.data}):((0,n.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_intersection_types}),n.INVALID)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then((([e,t])=>s(e,t))):s(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}e.ZodIntersection=U,U.create=(e,t,r)=>new U({left:e,right:t,typeName:ce.ZodIntersection,...l(r)});class V extends p{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==s.ZodParsedType.array)return(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.array,received:r.parsedType}),n.INVALID;if(r.data.length<this._def.items.length)return(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.INVALID;!this._def.rest&&r.data.length>this._def.items.length&&((0,n.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...r.data].map(((e,t)=>{const n=this._def.items[t]||this._def.rest;return n?n._parse(new c(r,e,r.path,t)):null})).filter((e=>!!e));return r.common.async?Promise.all(a).then((e=>n.ParseStatus.mergeArray(t,e))):n.ParseStatus.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new V({...this._def,rest:e})}}e.ZodTuple=V,V.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new V({items:e,typeName:ce.ZodTuple,rest:null,...l(t)})};class $ extends p{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==s.ZodParsedType.object)return(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.object,received:r.parsedType}),n.INVALID;const a=[],o=this._def.keyType,d=this._def.valueType;for(const e in r.data)a.push({key:o._parse(new c(r,e,r.path,e)),value:d._parse(new c(r,r.data[e],r.path,e))});return r.common.async?n.ParseStatus.mergeObjectAsync(t,a):n.ParseStatus.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,r){return new $(t instanceof p?{keyType:e,valueType:t,typeName:ce.ZodRecord,...l(r)}:{keyType:I.create(),valueType:e,typeName:ce.ZodRecord,...l(t)})}}e.ZodRecord=$;class q extends p{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==s.ZodParsedType.map)return(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.map,received:r.parsedType}),n.INVALID;const a=this._def.keyType,o=this._def.valueType,d=[...r.data.entries()].map((([e,t],n)=>({key:a._parse(new c(r,e,r.path,[n,"key"])),value:o._parse(new c(r,t,r.path,[n,"value"]))})));if(r.common.async){const e=new Map;return Promise.resolve().then((async()=>{for(const r of d){const s=await r.key,a=await r.value;if("aborted"===s.status||"aborted"===a.status)return n.INVALID;"dirty"!==s.status&&"dirty"!==a.status||t.dirty(),e.set(s.value,a.value)}return{status:t.value,value:e}}))}{const e=new Map;for(const r of d){const s=r.key,a=r.value;if("aborted"===s.status||"aborted"===a.status)return n.INVALID;"dirty"!==s.status&&"dirty"!==a.status||t.dirty(),e.set(s.value,a.value)}return{status:t.value,value:e}}}}e.ZodMap=q,q.create=(e,t,r)=>new q({valueType:t,keyType:e,typeName:ce.ZodMap,...l(r)});class W extends p{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==s.ZodParsedType.set)return(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.set,received:r.parsedType}),n.INVALID;const a=this._def;null!==a.minSize&&r.data.size<a.minSize.value&&((0,n.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),null!==a.maxSize&&r.data.size>a.maxSize.value&&((0,n.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());const o=this._def.valueType;function d(e){const r=new Set;for(const s of e){if("aborted"===s.status)return n.INVALID;"dirty"===s.status&&t.dirty(),r.add(s.value)}return{status:t.value,value:r}}const u=[...r.data.values()].map(((e,t)=>o._parse(new c(r,e,r.path,t))));return r.common.async?Promise.all(u).then((e=>d(e))):d(u)}min(e,t){return new W({...this._def,minSize:{value:e,message:r.errorUtil.toString(t)}})}max(e,t){return new W({...this._def,maxSize:{value:e,message:r.errorUtil.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}e.ZodSet=W,W.create=(e,t)=>new W({valueType:e,minSize:null,maxSize:null,typeName:ce.ZodSet,...l(t)});class H extends p{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==s.ZodParsedType.function)return(0,n.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.function,received:r.parsedType}),n.INVALID;function a(e,s){return(0,n.makeIssue)({data:e,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,(0,t.getErrorMap)(),t.defaultErrorMap].filter((e=>!!e)),issueData:{code:i.ZodIssueCode.invalid_arguments,argumentsError:s}})}function o(e,s){return(0,n.makeIssue)({data:e,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,(0,t.getErrorMap)(),t.defaultErrorMap].filter((e=>!!e)),issueData:{code:i.ZodIssueCode.invalid_return_type,returnTypeError:s}})}const d={errorMap:r.common.contextualErrorMap},c=r.data;return this._def.returns instanceof ee?(0,n.OK)((async(...e)=>{const t=new i.ZodError([]),r=await this._def.args.parseAsync(e,d).catch((r=>{throw t.addIssue(a(e,r)),t})),n=await c(...r);return await this._def.returns._def.type.parseAsync(n,d).catch((e=>{throw t.addIssue(o(n,e)),t}))})):(0,n.OK)(((...e)=>{const t=this._def.args.safeParse(e,d);if(!t.success)throw new i.ZodError([a(e,t.error)]);const r=c(...t.data),n=this._def.returns.safeParse(r,d);if(!n.success)throw new i.ZodError([o(r,n.error)]);return n.data}))}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new H({...this._def,args:V.create(e).rest(D.create())})}returns(e){return new H({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new H({args:e||V.create([]).rest(D.create()),returns:t||D.create(),typeName:ce.ZodFunction,...l(r)})}}e.ZodFunction=H;class J extends p{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}e.ZodLazy=J,J.create=(e,t)=>new J({getter:e,typeName:ce.ZodLazy,...l(t)});class G extends p{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{received:t.data,code:i.ZodIssueCode.invalid_literal,expected:this._def.value}),n.INVALID}return{status:"valid",value:e.data}}get value(){return this._def.value}}function X(e,t){return new Y({values:e,typeName:ce.ZodEnum,...l(t)})}e.ZodLiteral=G,G.create=(e,t)=>new G({value:e,typeName:ce.ZodLiteral,...l(t)});class Y extends p{_parse(e){if("string"!=typeof e.data){const t=this._getOrReturnCtx(e),r=this._def.values;return(0,n.addIssueToContext)(t,{expected:s.util.joinValues(r),received:t.parsedType,code:i.ZodIssueCode.invalid_type}),n.INVALID}if(-1===this._def.values.indexOf(e.data)){const t=this._getOrReturnCtx(e),r=this._def.values;return(0,n.addIssueToContext)(t,{received:t.data,code:i.ZodIssueCode.invalid_enum_value,options:r}),n.INVALID}return(0,n.OK)(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e){return Y.create(e)}exclude(e){return Y.create(this.options.filter((t=>!e.includes(t))))}}e.ZodEnum=Y,Y.create=X;class Q extends p{_parse(e){const t=s.util.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==s.ZodParsedType.string&&r.parsedType!==s.ZodParsedType.number){const e=s.util.objectValues(t);return(0,n.addIssueToContext)(r,{expected:s.util.joinValues(e),received:r.parsedType,code:i.ZodIssueCode.invalid_type}),n.INVALID}if(-1===t.indexOf(e.data)){const e=s.util.objectValues(t);return(0,n.addIssueToContext)(r,{received:r.data,code:i.ZodIssueCode.invalid_enum_value,options:e}),n.INVALID}return(0,n.OK)(e.data)}get enum(){return this._def.values}}e.ZodNativeEnum=Q,Q.create=(e,t)=>new Q({values:e,typeName:ce.ZodNativeEnum,...l(t)});class ee extends p{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==s.ZodParsedType.promise&&!1===t.common.async)return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.promise,received:t.parsedType}),n.INVALID;const r=t.parsedType===s.ZodParsedType.promise?t.data:Promise.resolve(t.data);return(0,n.OK)(r.then((e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap}))))}}e.ZodPromise=ee,ee.create=(e,t)=>new ee({type:e,typeName:ce.ZodPromise,...l(t)});class te extends p{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===ce.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:r}=this._processInputParams(e),a=this._def.effect||null;if("preprocess"===a.type){const e=a.transform(r.data);return r.common.async?Promise.resolve(e).then((e=>this._def.schema._parseAsync({data:e,path:r.path,parent:r}))):this._def.schema._parseSync({data:e,path:r.path,parent:r})}const i={addIssue:e=>{(0,n.addIssueToContext)(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),"refinement"===a.type){const e=e=>{const t=a.refinement(e,i);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===r.common.async){const s=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===s.status?n.INVALID:("dirty"===s.status&&t.dirty(),e(s.value),{status:t.value,value:s.value})}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then((r=>"aborted"===r.status?n.INVALID:("dirty"===r.status&&t.dirty(),e(r.value).then((()=>({status:t.value,value:r.value}))))))}if("transform"===a.type){if(!1===r.common.async){const e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!(0,n.isValid)(e))return e;const s=a.transform(e.value,i);if(s instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then((e=>(0,n.isValid)(e)?Promise.resolve(a.transform(e.value,i)).then((e=>({status:t.value,value:e}))):e))}s.util.assertNever(a)}}e.ZodEffects=te,e.ZodTransformer=te,te.create=(e,t,r)=>new te({schema:e,typeName:ce.ZodEffects,effect:t,...l(r)}),te.createWithPreprocess=(e,t,r)=>new te({schema:t,effect:{type:"preprocess",transform:e},typeName:ce.ZodEffects,...l(r)});class re extends p{_parse(e){return this._getType(e)===s.ZodParsedType.undefined?(0,n.OK)(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}e.ZodOptional=re,re.create=(e,t)=>new re({innerType:e,typeName:ce.ZodOptional,...l(t)});class ne extends p{_parse(e){return this._getType(e)===s.ZodParsedType.null?(0,n.OK)(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}e.ZodNullable=ne,ne.create=(e,t)=>new ne({innerType:e,typeName:ce.ZodNullable,...l(t)});class se extends p{_parse(e){const{ctx:t}=this._processInputParams(e);let r=t.data;return t.parsedType===s.ZodParsedType.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}e.ZodDefault=se,se.create=(e,t)=>new se({innerType:e,typeName:ce.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...l(t)});class ae extends p{_parse(e){const{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},s=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return(0,n.isAsync)(s)?s.then((e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new i.ZodError(r.common.issues)},input:r.data})}))):{status:"valid",value:"valid"===s.status?s.value:this._def.catchValue({get error(){return new i.ZodError(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}e.ZodCatch=ae,ae.create=(e,t)=>new ae({innerType:e,typeName:ce.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...l(t)});class ie extends p{_parse(e){if(this._getType(e)!==s.ZodParsedType.nan){const t=this._getOrReturnCtx(e);return(0,n.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:s.ZodParsedType.nan,received:t.parsedType}),n.INVALID}return{status:"valid",value:e.data}}}e.ZodNaN=ie,ie.create=e=>new ie({typeName:ce.ZodNaN,...l(e)}),e.BRAND=Symbol("zod_brand");class oe extends p{_parse(e){const{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}e.ZodBranded=oe;class de extends p{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?n.INVALID:"dirty"===e.status?(t.dirty(),(0,n.DIRTY)(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})()}{const e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?n.INVALID:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new de({in:e,out:t,typeName:ce.ZodPipeline})}}e.ZodPipeline=de;var ce;e.custom=(e,t={},r)=>e?O.create().superRefine(((n,s)=>{var a,i;if(!e(n)){const e="function"==typeof t?t(n):"string"==typeof t?{message:t}:t,o=null===(i=null!==(a=e.fatal)&&void 0!==a?a:r)||void 0===i||i,d="string"==typeof e?{message:e}:e;s.addIssue({code:"custom",...d,fatal:o})}})):O.create(),e.late={object:j.lazycreate},function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline"}(ce=e.ZodFirstPartyTypeKind||(e.ZodFirstPartyTypeKind={}));e.instanceof=(t,r={message:`Input not instance of ${t.name}`})=>(0,e.custom)((e=>e instanceof t),r);const ue=I.create;e.string=ue;const le=S.create;e.number=le;const pe=ie.create;e.nan=pe;const he=T.create;e.bigint=he;const me=P.create;e.boolean=me;const ge=E.create;e.date=ge;const fe=k.create;e.symbol=fe;const ve=N.create;e.undefined=ve;const ye=M.create;e.null=ye;const xe=O.create;e.any=xe;const we=D.create;e.unknown=we;const be=Z.create;e.never=be;const Ce=R.create;e.void=Ce;const Ie=z.create;e.array=Ie;const _e=j.create;e.object=_e;const Se=j.strictCreate;e.strictObject=Se;const Te=L.create;e.union=Te;const Pe=K.create;e.discriminatedUnion=Pe;const Ee=U.create;e.intersection=Ee;const ke=V.create;e.tuple=ke;const Ne=$.create;e.record=Ne;const Me=q.create;e.map=Me;const Oe=W.create;e.set=Oe;const De=H.create;e.function=De;const Ze=J.create;e.lazy=Ze;const Re=G.create;e.literal=Re;const ze=Y.create;e.enum=ze;const Ae=Q.create;e.nativeEnum=Ae;const je=ee.create;e.promise=je;const Le=te.create;e.effect=Le,e.transformer=Le;const Be=re.create;e.optional=Be;const Ke=ne.create;e.nullable=Ke;const Fe=te.createWithPreprocess;e.preprocess=Fe;const Ue=de.create;e.pipeline=Ue;e.ostring=()=>ue().optional();e.onumber=()=>le().optional();e.oboolean=()=>me().optional(),e.coerce={string:e=>I.create({...e,coerce:!0}),number:e=>S.create({...e,coerce:!0}),boolean:e=>P.create({...e,coerce:!0}),bigint:e=>T.create({...e,coerce:!0}),date:e=>E.create({...e,coerce:!0})},e.NEVER=n.INVALID}(x),function(t){var r=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),n=e&&e.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),n(a,t),n(f,t),n(v,t),n(o,t),n(x,t),n(d,t)}(s),function(t){var r=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),n=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=e&&e.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&r(t,e,s);return n(t,e),t},i=e&&e.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.z=void 0;const o=a(s);t.z=o,i(s,t),t.default=o}(n);var b={};!function(e){var t,r,n,s,a,i,o,d,c;Object.defineProperty(e,"__esModule",{value:!0}),e.Input=e.Network=e.Log=e.BrowsingContext=e.Script=e.Message=void 0,function(e){let t;!function(e){e.InvalidArgument="invalid argument",e.InvalidSessionId="invalid session id",e.MoveTargetOutOfBounds="move target out of bounds",e.NoSuchAlert="no such alert",e.NoSuchElement="no such element",e.NoSuchFrame="no such frame",e.NoSuchHandle="no such handle",e.NoSuchNode="no such node",e.NoSuchScript="no such script",e.SessionNotCreated="session not created",e.UnknownCommand="unknown command",e.UnknownError="unknown error",e.UnsupportedOperation="unsupported operation"}(t=e.ErrorCode||(e.ErrorCode={}));class r{error;message;stacktrace;constructor(e,t,r){this.error=e,this.message=t,this.stacktrace=r}toErrorResponse(e){return{id:e,error:this.error,message:this.message,stacktrace:this.stacktrace}}}e.ErrorResponse=r;e.InvalidArgumentException=class extends r{constructor(e,r){super(t.InvalidArgument,e,r)}};e.MoveTargetOutOfBoundsException=class extends r{constructor(e,r){super(t.MoveTargetOutOfBounds,e,r)}};e.NoSuchHandleException=class extends r{constructor(e,r){super(t.NoSuchHandle,e,r)}};e.InvalidSessionIdException=class extends r{constructor(e,r){super(t.InvalidSessionId,e,r)}};e.NoSuchAlertException=class extends r{constructor(e,r){super(t.NoSuchAlert,e,r)}};e.NoSuchFrameException=class extends r{constructor(e){super(t.NoSuchFrame,e)}};e.NoSuchNodeException=class extends r{constructor(e,r){super(t.NoSuchNode,e,r)}};e.NoSuchElementException=class extends r{constructor(e,r){super(t.NoSuchElement,e,r)}};e.NoSuchScriptException=class extends r{constructor(e,r){super(t.NoSuchScript,e,r)}};e.SessionNotCreatedException=class extends r{constructor(e,r){super(t.SessionNotCreated,e,r)}};e.UnknownCommandException=class extends r{constructor(e,r){super(t.UnknownCommand,e,r)}};e.UnknownErrorException=class extends r{constructor(e,r){super(t.UnknownError,e,r)}};e.UnsupportedOperationException=class extends r{constructor(e,r){super(t.UnsupportedOperation,e,r)}}}(e.Message||(e.Message={})),t=e.Script||(e.Script={}),(r=t.EventNames||(t.EventNames={})).MessageEvent="script.message",r.RealmCreated="script.realmCreated",r.RealmDestroyed="script.realmDestroyed",t.AllEvents="script",function(e){e.ContextCreatedEvent="browsingContext.contextCreated",e.ContextDestroyedEvent="browsingContext.contextDestroyed",e.DomContentLoadedEvent="browsingContext.domContentLoaded",e.FragmentNavigated="browsingContext.fragmentNavigated",e.LoadEvent="browsingContext.load",e.NavigationStarted="browsingContext.navigationStarted"}((n=e.BrowsingContext||(e.BrowsingContext={})).EventNames||(n.EventNames={})),n.AllEvents="browsingContext",(s=e.Log||(e.Log={})).AllEvents="log",function(e){e.LogEntryAddedEvent="log.entryAdded"}(s.EventNames||(s.EventNames={})),(a=e.Network||(e.Network={})).AllEvents="network",function(e){e.BeforeRequestSentEvent="network.beforeRequestSent",e.FetchErrorEvent="network.fetchError",e.ResponseStartedEvent="network.responseStarted",e.ResponseCompletedEvent="network.responseCompleted"}(a.EventNames||(a.EventNames={})),i=e.Input||(e.Input={}),(o=i.SourceActionsType||(i.SourceActionsType={})).None="none",o.Key="key",o.Pointer="pointer",o.Wheel="wheel",(d=i.PointerType||(i.PointerType={})).Mouse="mouse",d.Pen="pen",d.Touch="touch",(c=i.ActionType||(i.ActionType={})).Pause="pause",c.KeyDown="keyDown",c.KeyUp="keyUp",c.PointerUp="pointerUp",c.PointerDown="pointerDown",c.PointerMove="pointerMove",c.Scroll="scroll"}(b),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Input=e.Session=e.Cdp=e.BrowsingContext=e.Script=e.CommonDataTypes=e.parseObject=void 0;const t=n,r=b,s=9007199254740991;function a(e,t){const n=t.safeParse(e);if(n.success)return n.data;const s=n.error.errors.map((e=>`${e.message} in ${e.path.map((e=>JSON.stringify(e))).join("/")}.`)).join(" ");throw new r.Message.InvalidArgumentException(s)}e.parseObject=a;const i=t.z.string().refine((e=>1===[...e].length));var o;!function(e){e.SharedReferenceSchema=t.z.object({sharedId:t.z.string().min(1),handle:t.z.string().optional()}),e.RemoteReferenceSchema=t.z.object({handle:t.z.string().min(1)});const r=t.z.object({type:t.z.literal("undefined")}),n=t.z.object({type:t.z.literal("null")}),a=t.z.object({type:t.z.literal("string"),value:t.z.string()}),i=t.z.enum(["NaN","-0","Infinity","-Infinity"]),o=t.z.object({type:t.z.literal("number"),value:t.z.union([i,t.z.number()])}),d=t.z.object({type:t.z.literal("boolean"),value:t.z.boolean()}),c=t.z.object({type:t.z.literal("bigint"),value:t.z.string()}),u=t.z.union([r,n,a,o,d,c]);e.LocalValueSchema=t.z.lazy((()=>t.z.union([u,h,m,f,v,y,x])));const l=t.z.union([e.SharedReferenceSchema,e.RemoteReferenceSchema,e.LocalValueSchema]),p=t.z.array(l),h=t.z.object({type:t.z.literal("array"),value:p}),m=t.z.object({type:t.z.literal("date"),value:t.z.string().min(1)}),g=t.z.tuple([t.z.union([t.z.string(),l]),l]),f=t.z.object({type:t.z.literal("map"),value:t.z.array(g)}),v=t.z.object({type:t.z.literal("object"),value:t.z.array(g)}),y=t.z.object({type:t.z.literal("regexp"),value:t.z.object({pattern:t.z.string(),flags:t.z.string().optional()})}),x=t.z.lazy((()=>t.z.object({type:t.z.literal("set"),value:p})));e.BrowsingContextSchema=t.z.string(),e.MaxDepthSchema=t.z.number().int().nonnegative().max(s)}(o=e.CommonDataTypes||(e.CommonDataTypes={})),function(e){const r=t.z.enum(["window","dedicated-worker","shared-worker","service-worker","worker","paint-worklet","audio-worklet","worklet"]);e.GetRealmsParametersSchema=t.z.object({context:o.BrowsingContextSchema.optional(),type:r.optional()}),e.parseGetRealmsParams=function(t){return a(t,e.GetRealmsParametersSchema)};const n=t.z.object({context:o.BrowsingContextSchema,sandbox:t.z.string().optional()}),i=t.z.object({realm:t.z.string().min(1)}),d=t.z.union([i,n]),c=t.z.enum(["root","none"]),u=t.z.object({maxDomDepth:t.z.union([t.z.null(),t.z.number().int().nonnegative()]).optional(),maxObjectDepth:t.z.union([t.z.null(),t.z.number().int().nonnegative().max(s)]).optional(),includeShadowTree:t.z.enum(["none","open","all"]).optional()}),l=t.z.object({expression:t.z.string(),awaitPromise:t.z.boolean(),target:d,resultOwnership:c.optional(),serializationOptions:u.optional()});e.parseEvaluateParams=function(e){return a(e,l)};const p=t.z.object({target:d,handles:t.z.array(t.z.string())});e.parseDisownParams=function(e){return a(e,p)};const h=t.z.string(),m=t.z.object({channel:h,serializationOptions:u.optional(),ownership:c.optional()});e.ChannelValueSchema=t.z.object({type:t.z.literal("channel"),value:m}),e.PreloadScriptSchema=t.z.string(),e.AddPreloadScriptParametersSchema=t.z.object({functionDeclaration:t.z.string(),arguments:t.z.array(e.ChannelValueSchema).optional(),sandbox:t.z.string().optional(),context:o.BrowsingContextSchema.optional()}),e.parseAddPreloadScriptParams=function(t){return a(t,e.AddPreloadScriptParametersSchema)},e.RemovePreloadScriptParametersSchema=t.z.object({script:e.PreloadScriptSchema}),e.parseRemovePreloadScriptParams=function(t){return a(t,e.RemovePreloadScriptParametersSchema)};const g=t.z.union([o.RemoteReferenceSchema,o.SharedReferenceSchema,o.LocalValueSchema,e.ChannelValueSchema]),f=t.z.object({functionDeclaration:t.z.string(),awaitPromise:t.z.boolean(),target:d,arguments:t.z.array(g).optional(),resultOwnership:c.optional(),serializationOptions:u.optional(),this:g.optional()});e.parseCallFunctionParams=function(e){return a(e,f)}}(e.Script||(e.Script={})),function(e){const r=t.z.object({maxDepth:o.MaxDepthSchema.optional(),root:o.BrowsingContextSchema.optional()});e.parseGetTreeParams=function(e){return a(e,r)};const n=t.z.enum(["none","interactive","complete"]),s=t.z.object({context:o.BrowsingContextSchema,url:t.z.string().url(),wait:n.optional()});e.parseNavigateParams=function(e){return a(e,s)};const i=t.z.object({context:o.BrowsingContextSchema,ignoreCache:t.z.boolean().optional(),wait:n.optional()});e.parseReloadParams=function(e){return a(e,i)};const d=t.z.object({type:t.z.enum(["tab","window"]),referenceContext:o.BrowsingContextSchema.optional()});e.parseCreateParams=function(e){return a(e,d)};const c=t.z.object({context:o.BrowsingContextSchema});e.parseCloseParams=function(e){return a(e,c)};const u=t.z.object({context:o.BrowsingContextSchema});e.parseCaptureScreenshotParams=function(e){return a(e,u)};const l=t.z.object({height:t.z.number().nonnegative().optional(),width:t.z.number().nonnegative().optional()}),p=t.z.object({bottom:t.z.number().nonnegative().optional(),left:t.z.number().nonnegative().optional(),right:t.z.number().nonnegative().optional(),top:t.z.number().nonnegative().optional()}),h=t.z.array(t.z.union([t.z.string().min(1),t.z.number().int().nonnegative()])).refine((e=>e.every((e=>{const t=String(e).match(/^(?:(?:\d+)|(?:\d+[-])|(?:[-]\d+)|(?:(?<start>\d+)[-](?<end>\d+)))$/),{start:r,end:n}=t?.groups??{};return!(r&&n&&Number(r)>Number(n))&&t})))),m=t.z.object({context:o.BrowsingContextSchema,background:t.z.boolean().optional(),margin:p.optional(),orientation:t.z.enum(["portrait","landscape"]).optional(),page:l.optional(),pageRanges:h.optional(),scale:t.z.number().min(.1).max(2).optional(),shrinkToFit:t.z.boolean().optional()});e.parsePrintParams=function(e){return a(e,m)};const g=t.z.object({width:t.z.number().int().nonnegative(),height:t.z.number().int().nonnegative()}),f=t.z.object({context:o.BrowsingContextSchema,viewport:t.z.union([t.z.null(),g])});e.parseSetViewportParams=function(e){return a(e,f)}}(e.BrowsingContext||(e.BrowsingContext={})),function(e){const r=t.z.object({method:t.z.string(),params:t.z.object({}).passthrough().optional(),session:t.z.string().optional()});e.parseSendCommandParams=function(e){return a(e,r)};const n=t.z.object({context:o.BrowsingContextSchema});e.parseGetSessionParams=function(e){return a(e,n)}}(e.Cdp||(e.Cdp={})),function(e){const n=t.z.enum([r.BrowsingContext.AllEvents,...Object.values(r.BrowsingContext.EventNames),r.Log.AllEvents,...Object.values(r.Log.EventNames),r.Network.AllEvents,...Object.values(r.Network.EventNames),r.Script.AllEvents,...Object.values(r.Script.EventNames)]),s=t.z.custom((e=>"string"==typeof e&&e.startsWith("cdp.")),"Not a CDP event"),i=t.z.union([n,s]),d=t.z.object({events:t.z.array(i),contexts:t.z.array(o.BrowsingContextSchema).optional()});e.parseSubscribeParams=function(e){return a(e,d)}}(e.Session||(e.Session={})),function(e){const n=t.z.object({type:t.z.literal("element"),element:o.SharedReferenceSchema}),s=t.z.union([t.z.literal("viewport"),t.z.literal("pointer"),n]),d=t.z.object({type:t.z.literal(r.Input.ActionType.Pause),duration:t.z.number().nonnegative().int().optional()}),c=t.z.object({type:t.z.literal(r.Input.ActionType.KeyDown),value:i}),u=t.z.object({type:t.z.literal(r.Input.ActionType.KeyUp),value:i}),l=t.z.object({tiltX:t.z.number().min(-90).max(90).int().default(0).optional(),tiltY:t.z.number().min(-90).max(90).int().default(0).optional()}),p=t.z.object({altitudeAngle:t.z.number().nonnegative().max(Math.PI/2).default(0).optional(),azimuthAngle:t.z.number().nonnegative().max(2*Math.PI).default(0).optional()}),h=t.z.object({width:t.z.number().nonnegative().int().default(1),height:t.z.number().nonnegative().int().default(1),pressure:t.z.number().min(0).max(1).default(0),tangentialPressure:t.z.number().min(-1).max(1).default(0),twist:t.z.number().nonnegative().max(359).int().default(0)}).and(t.z.union([l,p])),m=t.z.object({type:t.z.literal(r.Input.ActionType.PointerUp),button:t.z.number().nonnegative().int()}).and(h),g=t.z.object({type:t.z.literal(r.Input.ActionType.PointerDown),button:t.z.number().nonnegative().int()}).and(h),f=t.z.object({type:t.z.literal(r.Input.ActionType.PointerMove),x:t.z.number().int(),y:t.z.number().int(),duration:t.z.number().nonnegative().int().optional(),origin:s.optional().default("viewport")}).and(h),v=t.z.object({type:t.z.literal(r.Input.ActionType.Scroll),x:t.z.number().int(),y:t.z.number().int(),deltaX:t.z.number().int(),deltaY:t.z.number().int(),duration:t.z.number().nonnegative().int().optional(),origin:s.optional().default("viewport")}),y=t.z.discriminatedUnion("type",[d,v]),x=t.z.object({type:t.z.literal(r.Input.SourceActionsType.Wheel),id:t.z.string(),actions:t.z.array(y)}),w=t.z.union([d,g,m,f]),b=t.z.nativeEnum(r.Input.PointerType),C=t.z.object({pointerType:b.optional().default(r.Input.PointerType.Mouse)}),I=t.z.object({type:t.z.literal(r.Input.SourceActionsType.Pointer),id:t.z.string(),parameters:C.optional(),actions:t.z.array(w)}),_=t.z.discriminatedUnion("type",[d,c,u]),S=t.z.object({type:t.z.literal(r.Input.SourceActionsType.Key),id:t.z.string(),actions:t.z.array(_)}),T=d,P=t.z.object({type:t.z.literal(r.Input.SourceActionsType.None),id:t.z.string(),actions:t.z.array(T)}),E=t.z.discriminatedUnion("type",[P,S,I,x]),k=t.z.object({context:o.BrowsingContextSchema,actions:t.z.array(E)});e.parsePerformActionsParams=function(e){return a(e,k)};const N=t.z.object({context:o.BrowsingContextSchema});e.parseReleaseActionsParams=function(e){return a(e,N)}}(e.Input||(e.Input={}))}(r);var C={},I={},_={},S=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(_,"__esModule",{value:!0}),_.EventEmitter=void 0;const T=S((function(e){return{all:e=e||new Map,on:function(t,r){var n=e.get(t);n?n.push(r):e.set(t,[r])},off:function(t,r){var n=e.get(t);n&&(r?n.splice(n.indexOf(r)>>>0,1):e.set(t,[]))},emit:function(t,r){var n=e.get(t);n&&n.slice().map((function(e){e(r)})),(n=e.get("*"))&&n.slice().map((function(e){e(t,r)}))}}}));_.EventEmitter=class{#e=(0,T.default)();on(e,t){return this.#e.on(e,t),this}once(e,t){const r=n=>{t(n),this.off(e,r)};return this.on(e,r)}off(e,t){return this.#e.off(e,t),this}emit(e,t){this.#e.emit(e,t)}};var P={};!function(e){var t;Object.defineProperty(e,"__esModule",{value:!0}),e.LogType=void 0,(t=e.LogType||(e.LogType={})).bidi="BiDi Messages",t.browsingContexts="Browsing Contexts",t.cdp="CDP",t.system="System"}(P);var E={};Object.defineProperty(E,"__esModule",{value:!0}),E.ProcessingQueue=void 0;const k=P;E.ProcessingQueue=class{#t;#r;#n=[];#s=!1;constructor(e,t){this.#r=e,this.#t=t}add(e){this.#n.push(e),this.#a()}async#a(){if(!this.#s){for(this.#s=!0;this.#n.length>0;){const e=this.#n.shift();void 0!==e&&await e.then((e=>this.#r(e))).catch((e=>{this.#t?.(k.LogType.system,"Event was not processed:",e)}))}this.#s=!1}}};var N={},M={},O={},D={};Object.defineProperty(D,"__esModule",{value:!0}),D.assert=void 0,D.assert=function(e){if(!e)throw new Error("Internal assertion failed.")};var Z={},R={};Object.defineProperty(R,"__esModule",{value:!0}),R.Mutex=void 0;R.Mutex=class{#i=!1;#o=[];acquire(){const e={resolved:!1};return this.#i?new Promise((t=>{this.#o.push((()=>t(this.#d.bind(this,e))))})):(this.#i=!0,Promise.resolve(this.#d.bind(this,e)))}#d(e){if(e.resolved)throw new Error("Cannot release more than once.");e.resolved=!0;const t=this.#o.shift();t?t():this.#i=!1}async run(e){const t=await this.acquire();try{return await e()}finally{t()}}};var z={};!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.WheelSource=e.PointerSource=e.KeySource=e.NoneSource=e.SourceType=void 0;const t=b;e.SourceType=t.Input.SourceActionsType;e.NoneSource=class{type=e.SourceType.None};e.KeySource=class{type=e.SourceType.Key;pressed=new Set;#c=0;get modifiers(){return this.#c}get alt(){return 1==(1&this.#c)}set alt(e){this.#u(e,1)}get ctrl(){return 2==(2&this.#c)}set ctrl(e){this.#u(e,2)}get meta(){return 4==(4&this.#c)}set meta(e){this.#u(e,4)}get shift(){return 8==(8&this.#c)}set shift(e){this.#u(e,8)}#u(e,t){e?this.#c|=t:this.#c&=~t}};class r{type=e.SourceType.Pointer;subtype;pointerId;pressed=new Set;x=0;y=0;constructor(e,t){this.pointerId=e,this.subtype=t}get buttons(){let e=0;for(const t of this.pressed)switch(t){case 0:e|=1;break;case 1:e|=4;break;case 2:e|=2;break;case 3:e|=8;break;case 4:e|=16}return e}static#l=500;static#p=2;#h=0;#m;setClickCount(e){(!this.#m||e.timeStamp-this.#m.timeStamp>r.#l||Math.abs(this.#m.x-e.x)>r.#p||Math.abs(this.#m.y-e.y)>r.#p)&&(this.#h=0),++this.#h,this.#m=e}get clickCount(){return this.#h}}e.PointerSource=r;e.WheelSource=class{type=e.SourceType.Wheel}}(z),Object.defineProperty(Z,"__esModule",{value:!0}),Z.InputState=void 0;const A=b,j=R,L=z;Z.InputState=class{cancelList=[];#g=new Map;#f=new j.Mutex;getOrCreate(e,t,r){let n=this.#g.get(e);if(!n){switch(t){case L.SourceType.None:n=new L.NoneSource;break;case L.SourceType.Key:n=new L.KeySource;break;case L.SourceType.Pointer:{let e=r===A.Input.PointerType.Mouse?0:2;const t=new Set;for(const[,e]of this.#g)e.type===L.SourceType.Pointer&&t.add(e.pointerId);for(;t.has(e);)++e;n=new L.PointerSource(e,r);break}case L.SourceType.Wheel:n=new L.WheelSource;break;default:throw new A.Message.InvalidArgumentException(`Expected "${L.SourceType.None}", "${L.SourceType.Key}", "${L.SourceType.Pointer}", or "${L.SourceType.Wheel}". Found unknown source type ${t}.`)}return this.#g.set(e,n),n}if(n.type!==t)throw new A.Message.InvalidArgumentException(`Input source type of ${e} is ${n.type}, but received ${t}.`);return n}get(e){const t=this.#g.get(e);if(!t)throw new A.Message.UnknownErrorException("Internal error.");return t}getGlobalKeyState(){const e=new L.KeySource;for(const[,t]of this.#g)if(t.type===L.SourceType.Key){for(const r of t.pressed)e.pressed.add(r);e.alt||=t.alt,e.ctrl||=t.ctrl,e.meta||=t.meta,e.shift||=t.shift}return e}get queue(){return this.#f}},Object.defineProperty(O,"__esModule",{value:!0}),O.InputStateManager=void 0;const B=D,K=Z;O.InputStateManager=class{#v=new WeakMap;get(e){(0,B.assert)(e.isTopLevelContext());let t=this.#v.get(e);return t||(t=new K.InputState,this.#v.set(e,t)),t}delete(e){this.#v.delete(e)}};var F={},U={};Object.defineProperty(U,"__esModule",{value:!0}),U.KeyToKeyCode=void 0,U.KeyToKeyCode={0:48,1:49,2:50,3:51,4:52,5:53,6:54,7:55,8:56,9:57,Abort:3,Help:6,Backspace:8,Tab:9,Numpad5:12,NumpadEnter:13,Enter:13,"\\r":13,"\\n":13,ShiftLeft:16,ShiftRight:16,ControlLeft:17,ControlRight:17,AltLeft:18,AltRight:18,Pause:19,CapsLock:20,Escape:27,Convert:28,NonConvert:29,Space:32,Numpad9:33,PageUp:33,Numpad3:34,PageDown:34,End:35,Numpad1:35,Home:36,Numpad7:36,ArrowLeft:37,Numpad4:37,Numpad8:38,ArrowUp:38,ArrowRight:39,Numpad6:39,Numpad2:40,ArrowDown:40,Select:41,Open:43,PrintScreen:44,Insert:45,Numpad0:45,Delete:46,NumpadDecimal:46,Digit0:48,Digit1:49,Digit2:50,Digit3:51,Digit4:52,Digit5:53,Digit6:54,Digit7:55,Digit8:56,Digit9:57,KeyA:65,KeyB:66,KeyC:67,KeyD:68,KeyE:69,KeyF:70,KeyG:71,KeyH:72,KeyI:73,KeyJ:74,KeyK:75,KeyL:76,KeyM:77,KeyN:78,KeyO:79,KeyP:80,KeyQ:81,KeyR:82,KeyS:83,KeyT:84,KeyU:85,KeyV:86,KeyW:87,KeyX:88,KeyY:89,KeyZ:90,MetaLeft:91,MetaRight:92,ContextMenu:93,NumpadMultiply:106,NumpadAdd:107,NumpadSubtract:109,NumpadDivide:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,F13:124,F14:125,F15:126,F16:127,F17:128,F18:129,F19:130,F20:131,F21:132,F22:133,F23:134,F24:135,NumLock:144,ScrollLock:145,AudioVolumeMute:173,AudioVolumeDown:174,AudioVolumeUp:175,MediaTrackNext:176,MediaTrackPrevious:177,MediaStop:178,MediaPlayPause:179,Semicolon:186,Equal:187,NumpadEqual:187,Comma:188,Minus:189,Period:190,Slash:191,Backquote:192,BracketLeft:219,Backslash:220,BracketRight:221,Quote:222,AltGraph:225,Props:247,Cancel:3,Clear:12,Shift:16,Control:17,Alt:18,Accept:30,ModeChange:31," ":32,Print:42,Execute:43,"\\u0000":46,a:65,b:66,c:67,d:68,e:69,f:70,g:71,h:72,i:73,j:74,k:75,l:76,m:77,n:78,o:79,p:80,q:81,r:82,s:83,t:84,u:85,v:86,w:87,x:88,y:89,z:90,Meta:91,"*":106,"+":107,"-":109,"/":111,";":186,"=":187,",":188,".":190,"`":192,"[":219,"\\\\":220,"]":221,"'":222,Attn:246,CrSel:247,ExSel:248,EraseEof:249,Play:250,ZoomOut:251,")":48,"!":49,"@":50,"#":51,$:52,"%":53,"^":54,"&":55,"(":57,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,":":186,"<":188,_:189,">":190,"?":191,"~":192,"{":219,"|":220,"}":221,'"':222,Camera:44,EndCall:95,VolumeDown:182,VolumeUp:183};var V={};Object.defineProperty(V,"__esModule",{value:!0}),V.getKeyLocation=V.getKeyCode=V.getNormalizedKey=void 0,V.getNormalizedKey=function(e){switch(e){case"\ue000":return"Unidentified";case"\ue001":return"Cancel";case"\ue002":return"Help";case"\ue003":return"Backspace";case"\ue004":return"Tab";case"\ue005":return"Clear";case"\ue006":return"Return";case"\ue007":return"Enter";case"\ue008":case"\ue050":return"Shift";case"\ue009":case"\ue051":return"Control";case"\ue00a":case"\ue052":return"Alt";case"\ue00b":return"Pause";case"\ue00c":return"Escape";case"\ue00d":return" ";case"\ue00e":case"\ue054":return"PageUp";case"\ue00f":case"\ue055":return"PageDown";case"\ue010":case"\ue056":return"End";case"\ue011":case"\ue057":return"Home";case"\ue012":case"\ue058":return"ArrowLeft";case"\ue013":case"\ue059":return"ArrowUp";case"\ue014":case"\ue05a":return"ArrowRight";case"\ue015":case"\ue05b":return"ArrowDown";case"\ue016":case"\ue05c":return"Insert";case"\ue017":case"\ue05d":return"Delete";case"\ue018":return";";case"\ue019":return"=";case"\ue01a":return"0";case"\ue01b":return"1";case"\ue01c":return"2";case"\ue01d":return"3";case"\ue01e":return"4";case"\ue01f":return"5";case"\ue020":return"6";case"\ue021":return"7";case"\ue022":return"8";case"\ue023":return"9";case"\ue024":return"*";case"\ue025":return"+";case"\ue026":return",";case"\ue027":return"-";case"\ue028":return".";case"\ue029":return"/";case"\ue031":return"F1";case"\ue032":return"F2";case"\ue033":return"F3";case"\ue034":return"F4";case"\ue035":return"F5";case"\ue036":return"F6";case"\ue037":return"F7";case"\ue038":return"F8";case"\ue039":return"F9";case"\ue03a":return"F10";case"\ue03b":return"F11";case"\ue03c":return"F12";case"\ue03d":case"\ue053":return"Meta";case"\ue040":return"ZenkakuHankaku";default:return e}},V.getKeyCode=function(e){switch(e){case"`":case"~":return"Backquote";case"\\":case"|":return"Backslash";case"\ue003":return"Backspace";case"[":case"{":return"BracketLeft";case"]":case"}":return"BracketRight";case",":case"<":return"Comma";case"0":case")":return"Digit0";case"1":case"!":return"Digit1";case"2":case"@":return"Digit2";case"3":case"#":return"Digit3";case"4":case"$":return"Digit4";case"5":case"%":return"Digit5";case"6":case"^":return"Digit6";case"7":case"&":return"Digit7";case"8":case"*":return"Digit8";case"9":case"(":return"Digit9";case"=":case"+":return"Equal";case"a":case"A":return"KeyA";case"b":case"B":return"KeyB";case"c":case"C":return"KeyC";case"d":case"D":return"KeyD";case"e":case"E":return"KeyE";case"f":case"F":return"KeyF";case"g":case"G":return"KeyG";case"h":case"H":return"KeyH";case"i":case"I":return"KeyI";case"j":case"J":return"KeyJ";case"k":case"K":return"KeyK";case"l":case"L":return"KeyL";case"m":case"M":return"KeyM";case"n":case"N":return"KeyN";case"o":case"O":return"KeyO";case"p":case"P":return"KeyP";case"q":case"Q":return"KeyQ";case"r":case"R":return"KeyR";case"s":case"S":return"KeyS";case"t":case"T":return"KeyT";case"u":case"U":return"KeyU";case"v":case"V":return"KeyV";case"w":case"W":return"KeyW";case"x":case"X":return"KeyX";case"y":case"Y":return"KeyY";case"z":case"Z":return"KeyZ";case"-":case"_":return"Minus";case".":return"Period";case"'":case'"':return"Quote";case";":case":":return"Semicolon";case"/":case"?":return"Slash";case"\ue00a":return"AltLeft";case"\ue052":return"AltRight";case"\ue009":return"ControlLeft";case"\ue051":return"ControlRight";case"\ue006":return"Enter";case"\ue03d":return"MetaLeft";case"\ue053":return"MetaRight";case"\ue008":return"ShiftLeft";case"\ue050":return"ShiftRight";case" ":case"\ue00d":return"Space";case"\ue004":return"Tab";case"\ue017":return"Delete";case"\ue010":return"End";case"\ue002":return"Help";case"\ue011":return"Home";case"\ue016":return"Insert";case"\ue00f":return"PageDown";case"\ue00e":return"PageUp";case"\ue015":return"ArrowDown";case"\ue012":return"ArrowLeft";case"\ue014":return"ArrowRight";case"\ue013":return"ArrowUp";case"\ue00c":return"Escape";case"\ue031":return"F1";case"\ue032":return"F2";case"\ue033":return"F3";case"\ue034":return"F4";case"\ue035":return"F5";case"\ue036":return"F6";case"\ue037":return"F7";case"\ue038":return"F8";case"\ue039":return"F9";case"\ue03a":return"F10";case"\ue03b":return"F11";case"\ue03c":return"F12";case"\ue01a":case"\ue05c":return"Numpad0";case"\ue01b":case"\ue056":return"Numpad1";case"\ue01c":case"\ue05b":return"Numpad2";case"\ue01d":case"\ue055":return"Numpad3";case"\ue01e":case"\ue058":return"Numpad4";case"\ue01f":return"Numpad5";case"\ue020":case"\ue05a":return"Numpad6";case"\ue021":case"\ue057":return"Numpad7";case"\ue022":case"\ue059":return"Numpad8";case"\ue023":case"\ue054":return"Numpad9";case"\ue025":return"NumpadAdd";case"\ue026":return"NumpadComma";case"\ue028":case"\ue05d":return"NumpadDecimal";case"\ue029":return"NumpadDivide";case"\ue007":return"NumpadEnter";case"\ue024":return"NumpadMultiply";case"\ue027":return"NumpadSubtract";default:return}},V.getKeyLocation=function(e){switch(e){case"\ue007":case"\ue008":case"\ue009":case"\ue00a":case"\ue03d":return 1;case"\ue01a":case"\ue01b":case"\ue01c":case"\ue01d":case"\ue01e":case"\ue01f":case"\ue020":case"\ue021":case"\ue022":case"\ue023":case"\ue024":case"\ue025":case"\ue026":case"\ue027":case"\ue028":case"\ue029":case"\ue054":case"\ue055":case"\ue056":case"\ue057":case"\ue058":case"\ue059":case"\ue05a":case"\ue05b":case"\ue05c":case"\ue05d":return 3;case"\ue050":case"\ue051":case"\ue052":case"\ue053":return 2;default:return 0}},Object.defineProperty(F,"__esModule",{value:!0}),F.ActionDispatcher=void 0;const $=b,q=D,W=U,H=V,J=(e=>{const t=e.getClientRects()[0],r=Math.max(0,Math.min(t.x,t.x+t.width)),n=Math.min(window.innerWidth,Math.max(t.x,t.x+t.width)),s=Math.max(0,Math.min(t.y,t.y+t.height));return[r+(n-r>>1),s+(Math.min(window.innerHeight,Math.max(t.y,t.y+t.height))-s>>1)]}).toString(),G=(()=>navigator.platform.toLowerCase().includes("mac")).toString();F.ActionDispatcher=class{static isMacOS=async e=>{const{result:t}=await(await e.getOrCreateSandbox(void 0)).callFunction(G,{type:"undefined"},[],!1,"none",{});return(0,q.assert)("exception"!==t.type),(0,q.assert)("boolean"===t.result.type),t.result.value};#y=0;#x=0;#w;#b;#C;constructor(e,t,r){this.#w=e,this.#b=t,this.#C=r}async dispatchActions(e){await this.#w.queue.run((async()=>{for(const t of e)await this.dispatchTickActions(t)}))}async dispatchTickActions(e){this.#y=performance.now(),this.#x=0;for(const{action:t}of e)"duration"in t&&void 0!==t.duration&&(this.#x=Math.max(this.#x,t.duration));const t=[new Promise((e=>setTimeout(e,this.#x)))];for(const r of e)t.push(this.#I(r));await Promise.all(t)}async#I({id:e,action:t}){const r=this.#w.get(e),n=this.#w.getGlobalKeyState();switch(t.type){case $.Input.ActionType.KeyDown:await this.#_(r,t),this.#w.cancelList.push({id:e,action:{...t,type:$.Input.ActionType.KeyUp}});break;case $.Input.ActionType.KeyUp:await this.#S(r,t);break;case $.Input.ActionType.Pause:break;case $.Input.ActionType.PointerDown:await this.#T(r,n,t),this.#w.cancelList.push({id:e,action:{...t,type:$.Input.ActionType.PointerUp}});break;case $.Input.ActionType.PointerMove:await this.#P(r,n,t);break;case $.Input.ActionType.PointerUp:await this.#E(r,n,t);break;case $.Input.ActionType.Scroll:await this.#k(r,n,t)}}#T(e,t,r){const{button:n}=r;if(e.pressed.has(n))return;e.pressed.add(n);const{x:s,y:a,subtype:i}=e,{width:o,height:d,pressure:c,twist:u,tangentialPressure:l}=r,{tiltX:p,tiltY:h}="tiltX"in r?r:{},{modifiers:m}=t;switch(i){case $.Input.PointerType.Mouse:case $.Input.PointerType.Pen:return e.setClickCount({x:s,y:a,timeStamp:performance.now()}),this.#b.cdpTarget.cdpClient.sendCommand("Input.dispatchMouseEvent",{type:"mousePressed",x:s,y:a,modifiers:m,button:(()=>{switch(n){case 0:return"left";case 1:return"middle";case 2:return"right";case 3:return"back";case 4:return"forward";default:return"none"}})(),buttons:e.buttons,clickCount:e.clickCount,pointerType:i,tangentialPressure:l,tiltX:p,tiltY:h,twist:u,force:c});case $.Input.PointerType.Touch:return this.#b.cdpTarget.cdpClient.sendCommand("Input.dispatchTouchEvent",{type:"touchStart",touchPoints:[{x:s,y:a,radiusX:o,radiusY:d,tangentialPressure:l,tiltX:p,tiltY:h,twist:u,force:c,id:e.pointerId}],modifiers:m})}}#E(e,t,r){const{button:n}=r;if(!e.pressed.has(n))return;e.pressed.delete(n);const{x:s,y:a,subtype:i}=e,{modifiers:o}=t;switch(i){case $.Input.PointerType.Mouse:case $.Input.PointerType.Pen:return this.#b.cdpTarget.cdpClient.sendCommand("Input.dispatchMouseEvent",{type:"mouseReleased",x:s,y:a,modifiers:o,button:(()=>{switch(n){case 0:return"left";case 1:return"middle";case 2:return"right";case 3:return"back";case 4:return"forward";default:return"none"}})(),buttons:e.buttons,clickCount:e.clickCount,pointerType:i});case $.Input.PointerType.Touch:return this.#b.cdpTarget.cdpClient.sendCommand("Input.dispatchTouchEvent",{type:"touchEnd",touchPoints:[{x:s,y:a,id:e.pointerId}],modifiers:o})}}async#P(e,t,r){const{x:n,y:s,subtype:a}=e,{width:i,height:o,pressure:d,twist:c,tangentialPressure:u,x:l,y:p,origin:h="viewport",duration:m=this.#x}=r,{tiltX:g,tiltY:f}="tiltX"in r?r:{},{targetX:v,targetY:y}=await this.#N(h,l,p,n,s);if(v<0||y<0)throw new $.Message.MoveTargetOutOfBoundsException(`Cannot move beyond viewport (x: ${v}, y: ${y})`);let x;do{const r=m>0?(performance.now()-this.#y)/m:1;let l,p;if(x=r>=1,x?(l=v,p=y):(l=Math.round(r*(v-n)+n),p=Math.round(r*(y-s)+s)),e.x!==l||e.y!==p){const{modifiers:r}=t;switch(a){case $.Input.PointerType.Mouse:case $.Input.PointerType.Pen:await this.#b.cdpTarget.cdpClient.sendCommand("Input.dispatchMouseEvent",{type:"mouseMoved",x:l,y:p,modifiers:r,clickCount:0,buttons:e.buttons,pointerType:a,tangentialPressure:u,tiltX:g,tiltY:f,twist:c,force:d});break;case $.Input.PointerType.Touch:await this.#b.cdpTarget.cdpClient.sendCommand("Input.dispatchTouchEvent",{type:"touchMove",touchPoints:[{x:l,y:p,radiusX:i,radiusY:o,tangentialPressure:u,tiltX:g,tiltY:f,twist:c,force:d,id:e.pointerId}],modifiers:r})}e.x=l,e.y=p}}while(!x)}async#N(e,t,r,n,s){let a,i;switch(e){case"viewport":a=t,i=r;break;case"pointer":a=n+t,i=s+r;break;default:{const{x:n,y:s}=await async function(e,t){const{result:r}=await(await e.getOrCreateSandbox(void 0)).callFunction(J,{type:"undefined"},[t],!1,"none",{});if("exception"===r.type)throw new $.Message.NoSuchElementException(`Origin element ${t.sharedId} was not found`);(0,q.assert)("array"===r.result.type),(0,q.assert)("number"===r.result.value?.[0]?.type),(0,q.assert)("number"===r.result.value?.[1]?.type);const{result:{value:[{value:n},{value:s}]}}=r;return{x:n,y:s}}(this.#b,e.element);a=n+t,i=s+r;break}}return{targetX:a,targetY:i}}async#k(e,t,r){const{deltaX:n,deltaY:s,x:a,y:i,origin:o="viewport",duration:d=this.#x}=r;if("pointer"===o)throw new $.Message.InvalidArgumentException('"pointer" origin is invalid for scrolling.');const{targetX:c,targetY:u}=await this.#N(o,a,i,0,0);if(c<0||u<0)throw new $.Message.MoveTargetOutOfBoundsException(`Cannot move beyond viewport (x: ${c}, y: ${u})`);let l,p=0,h=0;do{const e=d>0?(performance.now()-this.#y)/d:1;let r,a;if(l=e>=1,l?(r=n-p,a=s-h):(r=Math.round(e*n-p),a=Math.round(e*s-h)),0!==r||0!==a){const{modifiers:e}=t;await this.#b.cdpTarget.cdpClient.sendCommand("Input.dispatchMouseEvent",{type:"mouseWheel",deltaX:r,deltaY:a,x:c,y:u,modifiers:e}),p+=r,h+=a}}while(!l)}#_(e,t){const r=t.value,n=(0,H.getNormalizedKey)(r),s=e.pressed.has(n),a=(0,H.getKeyCode)(r),i=(0,H.getKeyLocation)(r);switch(n){case"Alt":e.alt=!0;break;case"Shift":e.shift=!0;break;case"Control":e.ctrl=!0;break;case"Meta":e.meta=!0}e.pressed.add(n);const{modifiers:o}=e,d=X(n,e),c=Y(a??"",e)??d;let u;if(this.#C&&e.meta)switch(a){case"KeyA":u="SelectAll";break;case"KeyC":u="Copy";break;case"KeyV":u=e.shift?"PasteAndMatchStyle":"Paste";break;case"KeyX":u="Cut";break;case"KeyZ":u=e.shift?"Redo":"Undo"}return this.#b.cdpTarget.cdpClient.sendCommand("Input.dispatchKeyEvent",{type:c?"keyDown":"rawKeyDown",windowsVirtualKeyCode:W.KeyToKeyCode[n],key:n,code:a,text:c,unmodifiedText:d,autoRepeat:s,isSystemKey:e.alt||void 0,location:i<3?i:void 0,isKeypad:3===i,modifiers:o,commands:u?[u]:void 0})}#S(e,t){const r=t.value,n=(0,H.getNormalizedKey)(r);if(!e.pressed.has(n))return;const s=(0,H.getKeyCode)(r),a=(0,H.getKeyLocation)(r);switch(n){case"Alt":e.alt=!1;break;case"Shift":e.shift=!1;break;case"Control":e.ctrl=!1;break;case"Meta":e.meta=!1}e.pressed.delete(n);const{modifiers:i}=e,o=X(n,e),d=Y(s??"",e)??o;return this.#b.cdpTarget.cdpClient.sendCommand("Input.dispatchKeyEvent",{type:"keyUp",windowsVirtualKeyCode:W.KeyToKeyCode[n],key:n,code:s,text:d,unmodifiedText:o,location:a<3?a:void 0,isSystemKey:e.alt||void 0,isKeypad:3===a,modifiers:i})}};const X=(e,t)=>"Enter"===e?"\r":1===[...e].length?t.shift?e.toLocaleUpperCase("en-US"):e:void 0,Y=(e,t)=>{if(t.ctrl){switch(e){case"Digit2":if(t.shift)return"\0";break;case"KeyA":return"\x01";case"KeyB":return"\x02";case"KeyC":return"\x03";case"KeyD":return"\x04";case"KeyE":return"\x05";case"KeyF":return"\x06";case"KeyG":return"\x07";case"KeyH":return"\b";case"KeyI":return"\t";case"KeyJ":return"\n";case"KeyK":return"\v";case"KeyL":return"\f";case"KeyM":return"\r";case"KeyN":return"\x0e";case"KeyO":return"\x0f";case"KeyP":return"\x10";case"KeyQ":return"\x11";case"KeyR":return"\x12";case"KeyS":return"\x13";case"KeyT":return"\x14";case"KeyU":return"\x15";case"KeyV":return"\x16";case"KeyW":return"\x17";case"KeyX":return"\x18";case"KeyY":return"\x19";case"KeyZ":return"\x1a";case"BracketLeft":return"\x1b";case"Backslash":return"\x1c";case"BracketRight":return"\x1d";case"Digit6":if(t.shift)return"\x1e";break;case"Minus":return"\x1f"}return""}if(t.alt)return""};var Q={};Object.defineProperty(Q,"__esModule",{value:!0}),Q.PreloadScriptStorage=void 0;Q.PreloadScriptStorage=class{#M=new Set;findPreloadScripts(e){return e?[...this.#M].filter((t=>(void 0===e.id||e.id===t.id)&&((void 0===e.contextId||e.contextId===t.contextId)&&(!(void 0!==e.contextIds&&!e.contextIds.includes(t.contextId))&&!(void 0!==e.targetId&&!t.targetIds.has(e.targetId)))))):[...this.#M]}addPreloadScript(e){this.#M.add(e)}removeBiDiPreloadScripts(e){for(const t of this.findPreloadScripts(e))this.#M.delete(t)}};var ee={},te={};Object.defineProperty(te,"__esModule",{value:!0}),te.inchesFromCm=void 0,te.inchesFromCm=function(e){return e/2.54};var re={};Object.defineProperty(re,"__esModule",{value:!0}),re.Deferred=void 0;class ne{#O=!1;#D;#Z;#R;get isFinished(){return this.#O}constructor(){this.#D=new Promise(((e,t)=>{this.#Z=e,this.#R=t})),this.#D.catch((e=>{}))}then(e,t){return this.#D.then(e,t)}catch(e){return this.#D.catch(e)}resolve(e){this.#O=!0,this.#Z?.(e)}reject(e){this.#O=!0,this.#R?.(e)}finally(e){return this.#D.finally(e)}[Symbol.toStringTag]="Promise"}re.Deferred=ne;var se={},ae={},ie={},oe={};Object.defineProperty(oe,"__esModule",{value:!0}),oe.uuidv4=void 0,oe.uuidv4=function(){if("crypto"in globalThis&&"randomUUID"in globalThis.crypto)return globalThis.crypto.randomUUID();const e=new Uint8Array(16);"crypto"in globalThis&&"getRandomValues"in globalThis.crypto?globalThis.crypto.getRandomValues(e):require("crypto").webcrypto.getRandomValues(e),e[6]=15&e[6]|64,e[8]=63&e[8]|128;const t=e=>e.reduce(((e,t)=>e+t.toString(16).padStart(2,"0")),"");return[t(e.subarray(0,4)),t(e.subarray(4,6)),t(e.subarray(6,8)),t(e.subarray(8,10)),t(e.subarray(10,16))].join("-")},Object.defineProperty(ie,"__esModule",{value:!0}),ie.ChannelProxy=void 0;const de=b,ce=oe;class ue{#z;#A=(0,ce.uuidv4)();constructor(e){if(![0,null,void 0].includes(e.serializationOptions?.maxDomDepth))throw new Error("serializationOptions.maxDomDepth other than 0 or null is not supported");if(![void 0,"none"].includes(e.serializationOptions?.includeShadowTree))throw new Error('serializationOptions.includeShadowTree other than "none" is not supported');this.#z=e}async init(e,t){const r=await ue.#j(e),n=await ue.#L(e,r);return this.#B(e,r,t),n}async startListenerFromWindow(e,t){const r=await this.#K(e);this.#B(e,r,t)}static#F(){return`(${String((()=>{const e=[];let t=null;return{async getMessage(){const r=e.length>0?Promise.resolve():new Promise((e=>{t=e}));return await r,e.shift()},sendMessage(r){e.push(r),null!==t&&(t(),t=null)}}}))})()`}static async#j(e){const t=await e.cdpClient.sendCommand("Runtime.evaluate",{expression:this.#F(),contextId:e.executionContextId,serializationOptions:{serialization:"idOnly"}});if(t.exceptionDetails||void 0===t.result.objectId)throw new Error("Cannot create channel");return t.result.objectId}static async#L(e,t){return(await e.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((e=>e.sendMessage)),arguments:[{objectId:t}],executionContextId:e.executionContextId,serializationOptions:{serialization:"idOnly"}})).result.objectId}async#B(e,t,r){for(;;){const n=await e.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((async e=>e.getMessage())),arguments:[{objectId:t}],awaitPromise:!0,executionContextId:e.executionContextId,serializationOptions:{serialization:"deep",...void 0===this.#z.serializationOptions?.maxObjectDepth||null===this.#z.serializationOptions.maxObjectDepth?{}:{maxDepth:this.#z.serializationOptions.maxObjectDepth}}});if(n.exceptionDetails)return;r.registerEvent({method:de.Script.EventNames.MessageEvent,params:{channel:this.#z.channel,data:e.cdpToBidiValue(n,this.#z.ownership??"none"),source:{realm:e.realmId,context:e.browsingContextId}}},e.browsingContextId)}}async#K(e){const t=await e.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((e=>{const t=window;if(void 0===t[e])return new Promise((r=>t[e]=r));const r=t[e];return delete t[e],r})),arguments:[{value:this.#A}],executionContextId:e.executionContextId,awaitPromise:!0,serializationOptions:{serialization:"idOnly"}});if(void 0!==t.exceptionDetails||void 0===t.result.objectId)throw new Error(`ChannelHandle not found in window["${this.#A}"]`);return t.result.objectId}getEvalInWindowStr(){const e=String(((e,t)=>{const r=window;return void 0===r[e]?r[e]=t:(r[e](t),delete r[e]),t.sendMessage})),t=ue.#F();return`(${e})('${this.#A}',${t})`}}ie.ChannelProxy=ue,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ScriptEvaluator=e.SHARED_ID_DIVIDER=void 0;const t=b,r=ie;e.SHARED_ID_DIVIDER="_element_";class n{#U;constructor(e){this.#U=e}static async stringifyObject(e,t){return(await t.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((e=>String(e))),awaitPromise:!1,arguments:[e],returnByValue:!0,executionContextId:t.executionContextId})).result.value}async serializeCdpObject(e,t,r){const s=n.#V(e),a=await r.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((e=>e)),awaitPromise:!1,arguments:[s],serializationOptions:{serialization:"deep"},executionContextId:r.executionContextId});return r.cdpToBidiValue(a,t)}async scriptEvaluate(e,t,r,n,s){if(![0,null,void 0].includes(s.maxDomDepth))throw new Error("serializationOptions.maxDomDepth other than 0 or null is not supported");const a=await e.cdpClient.sendCommand("Runtime.evaluate",{contextId:e.executionContextId,expression:t,awaitPromise:r,serializationOptions:{serialization:"deep",...void 0===s.maxObjectDepth||null===s.maxObjectDepth?{}:{maxDepth:s.maxObjectDepth}}});return a.exceptionDetails?{exceptionDetails:await this.#$(a.exceptionDetails,0,n,e),type:"exception",realm:e.realmId}:{type:"success",result:e.cdpToBidiValue(a,n),realm:e.realmId}}async callFunction(e,r,n,s,a,i,o){if(![0,null,void 0].includes(o.maxDomDepth))throw new Error("serializationOptions.maxDomDepth other than 0 or null is not supported");const d=`(...args)=>{ return _callFunction((\n${r}\n), args);\n      function _callFunction(f, args) {\n        const deserializedThis = args.shift();\n        const deserializedArgs = args;\n        return f.apply(deserializedThis, deserializedArgs);\n      }}`,c=[await this.#q(n,e)];let u;c.push(...await Promise.all(s.map((async t=>this.#q(t,e)))));try{u=await e.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:d,awaitPromise:a,arguments:c,serializationOptions:{serialization:"deep",...void 0===o.maxObjectDepth||null===o.maxObjectDepth?{}:{maxDepth:o.maxObjectDepth}},executionContextId:e.executionContextId})}catch(e){if(-32e3===e.code&&["Could not find object with given id","Argument should belong to the same JavaScript world as target object","Invalid remote object id"].includes(e.message))throw new t.Message.NoSuchHandleException("Handle was not found.");throw e}return u.exceptionDetails?{exceptionDetails:await this.#$(u.exceptionDetails,1,i,e),type:"exception",realm:e.realmId}:{type:"success",result:e.cdpToBidiValue(u,i),realm:e.realmId}}static#V(e){return void 0!==e.objectId?{objectId:e.objectId}:void 0!==e.unserializableValue?{unserializableValue:e.unserializableValue}:{value:e.value}}async#q(n,s){if("sharedId"in n){const[r,a]=n.sharedId.split(e.SHARED_ID_DIVIDER),i=parseInt(a??"");if(isNaN(i)||void 0===i||void 0===r)throw new t.Message.NoSuchNodeException(`SharedId "${n.sharedId}" was not found.`);if(s.navigableId!==r)throw new t.Message.NoSuchNodeException(`SharedId "${n.sharedId}" belongs to different document. Current document is ${s.navigableId}.`);try{return{objectId:(await s.cdpClient.sendCommand("DOM.resolveNode",{backendNodeId:i,executionContextId:s.executionContextId})).object.objectId}}catch(e){if(-32e3===e.code&&"No node with given id found"===e.message)throw new t.Message.NoSuchNodeException(`SharedId "${n.sharedId}" was not found.`);throw e}}if("handle"in n)return{objectId:n.handle};switch(n.type){case"undefined":return{unserializableValue:"undefined"};case"null":return{unserializableValue:"null"};case"string":return{value:n.value};case"number":return"NaN"===n.value?{unserializableValue:"NaN"}:"-0"===n.value?{unserializableValue:"-0"}:"Infinity"===n.value?{unserializableValue:"Infinity"}:"-Infinity"===n.value?{unserializableValue:"-Infinity"}:{value:n.value};case"boolean":return{value:Boolean(n.value)};case"bigint":return{unserializableValue:`BigInt(${JSON.stringify(n.value)})`};case"date":return{unserializableValue:`new Date(Date.parse(${JSON.stringify(n.value)}))`};case"regexp":return{unserializableValue:`new RegExp(${JSON.stringify(n.value.pattern)}, ${JSON.stringify(n.value.flags)})`};case"map":{const e=await this.#W(n.value,s);return{objectId:(await s.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String(((...e)=>{const t=new Map;for(let r=0;r<e.length;r+=2)t.set(e[r],e[r+1]);return t})),awaitPromise:!1,arguments:e,returnByValue:!1,executionContextId:s.executionContextId})).result.objectId}}case"object":{const e=await this.#W(n.value,s);return{objectId:(await s.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String(((...e)=>{const t={};for(let r=0;r<e.length;r+=2){t[e[r]]=e[r+1]}return t})),awaitPromise:!1,arguments:e,returnByValue:!1,executionContextId:s.executionContextId})).result.objectId}}case"array":{const e=await this.#H(n.value,s);return{objectId:(await s.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String(((...e)=>e)),awaitPromise:!1,arguments:e,returnByValue:!1,executionContextId:s.executionContextId})).result.objectId}}case"set":{const e=await this.#H(n.value,s);return{objectId:(await s.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String(((...e)=>new Set(e))),awaitPromise:!1,arguments:e,returnByValue:!1,executionContextId:s.executionContextId})).result.objectId}}case"channel":{const e=new r.ChannelProxy(n.value);return{objectId:await e.init(s,this.#U)}}default:throw new Error(`Value ${JSON.stringify(n)} is not deserializable.`)}}async#W(e,t){const r=[];for(const[n,s]of e){let e;e="string"==typeof n?{value:n}:await this.#q(n,t);const a=await this.#q(s,t);r.push(e),r.push(a)}return r}async#H(e,t){return Promise.all(e.map((e=>this.#q(e,t))))}async#$(e,t,r,s){const a=e.stackTrace?.callFrames.map((e=>({url:e.url,functionName:e.functionName,lineNumber:e.lineNumber-t,columnNumber:e.columnNumber}))),i=await this.serializeCdpObject(e.exception,r,s),o=await n.stringifyObject(e.exception,s);return{exception:i,columnNumber:e.columnNumber,lineNumber:e.lineNumber-t,stackTrace:{callFrames:a??[]},text:o||e.text}}}e.ScriptEvaluator=n}(ae),Object.defineProperty(se,"__esModule",{value:!0}),se.Realm=void 0;const le=b,pe=P,he=ae;se.Realm=class{#J;#G;#X;#Y;#Q;#ee;#te;#re;#U;#ne;sandbox;cdpSessionId;#t;constructor(e,t,r,n,s,a,i,o,d,c,u,l){this.#X=r,this.#Y=n,this.#Q=s,this.sandbox=o,this.#ee=a,this.#te=i,this.cdpSessionId=d,this.#re=c,this.#J=e,this.#G=t,this.#U=u,this.#ne=new he.ScriptEvaluator(this.#U),this.#J.addRealm(this),this.#t=l,this.#U.registerEvent({method:le.Script.EventNames.RealmCreated,params:this.toBiDi()},this.browsingContextId)}async#se(e){try{await this.cdpClient.sendCommand("Runtime.releaseObject",{objectId:e})}catch(e){if(-32e3!==e.code||"Invalid remote object id"!==e.message)throw e}}async disown(e){this.#J.knownHandlesToRealm.get(e)===this.realmId&&(await this.#se(e),this.#J.knownHandlesToRealm.delete(e))}cdpToBidiValue(e,t){const r=e.result.deepSerializedValue,n=this.deepSerializedToBiDi(r);if(e.result.objectId){const r=e.result.objectId;"root"===t?(n.handle=r,this.#J.knownHandlesToRealm.set(r,this.realmId)):this.#se(r).catch((e=>this.#t?.(pe.LogType.system,e)))}return n}deepSerializedToBiDi(e){const t=e;if(Object.hasOwn(t,"weakLocalObjectReference")&&(t.internalId=`${t.weakLocalObjectReference}`,delete t.weakLocalObjectReference),"platformobject"===t.type)return{type:"object"};const r=t.value;if(void 0===r)return t;if("node"===t.type&&(Object.hasOwn(r,"backendNodeId")&&(t.sharedId=`${this.navigableId}${he.SHARED_ID_DIVIDER}${r.backendNodeId}`,delete r.backendNodeId),Object.hasOwn(r,"children")))for(const e in r.children)r.children[e]=this.deepSerializedToBiDi(r.children[e]);if(["array","set"].includes(e.type))for(const e in r)r[e]=this.deepSerializedToBiDi(r[e]);if(["object","map"].includes(e.type))for(const e in r)r[e]=[this.deepSerializedToBiDi(r[e][0]),this.deepSerializedToBiDi(r[e][1])];return t}toBiDi(){return{realm:this.realmId,origin:this.origin,type:this.type,context:this.browsingContextId,...void 0===this.sandbox?{}:{sandbox:this.sandbox}}}get realmId(){return this.#X}get navigableId(){return this.#G.findContext(this.#Y)?.navigableId??"UNKNOWN"}get browsingContextId(){return this.#Y}get executionContextId(){return this.#Q}get origin(){return this.#ee}get type(){return this.#te}get cdpClient(){return this.#re}async callFunction(e,t,r,n,s,a){const i=this.#G.getContext(this.browsingContextId);return await i.awaitUnblocked(),{result:await this.#ne.callFunction(this,e,t,r,n,s,a)}}async scriptEvaluate(e,t,r,n){const s=this.#G.getContext(this.browsingContextId);return await s.awaitUnblocked(),{result:await this.#ne.scriptEvaluate(this,e,t,r,n)}}async serializeCdpObject(e,t){return this.#ne.serializeCdpObject(e,t,this)}async stringifyObject(e){return he.ScriptEvaluator.stringifyObject(e,this)}delete(){this.#U.registerEvent({method:le.Script.EventNames.RealmDestroyed,params:{realm:this.realmId}},this.browsingContextId)}},Object.defineProperty(ee,"__esModule",{value:!0}),ee.BrowsingContextImpl=void 0;const me=te,ge=b,fe=P,ve=re,ye=se;class xe{#A;#ae;#ie=new Set;#G;#oe={documentInitialized:new ve.Deferred,Page:{navigatedWithinDocument:new ve.Deferred,lifecycleEvent:{DOMContentLoaded:new ve.Deferred,load:new ve.Deferred}}};#de="about:blank";#U;#J;#ce;#ue;#le;#pe=!1;#t;constructor(e,t,r,n,s,a,i){this.#ue=e,this.#J=t,this.#A=r,this.#ae=n,this.#U=s,this.#G=a,this.#t=i}static create(e,t,r,n,s,a,i){const o=new xe(e,t,r,n,s,a,i);return o.#he(),a.addContext(o),o.isTopLevelContext()||o.parent.addChild(o.id),s.registerEvent({method:ge.BrowsingContext.EventNames.ContextCreatedEvent,params:o.serializeToBidiValue()},o.id),o}static getTimestamp(){return(new Date).getTime()}get navigableId(){return this.#ce}delete(){this.#me(),this.#J.deleteRealms({browsingContextId:this.id}),this.isTopLevelContext()||this.parent.#ie.delete(this.id),this.#U.registerEvent({method:ge.BrowsingContext.EventNames.ContextDestroyedEvent,params:this.serializeToBidiValue()},this.id),this.#G.deleteContextById(this.id)}get id(){return this.#A}get parentId(){return this.#ae}get parent(){return null===this.parentId?null:this.#G.getContext(this.parentId)}get directChildren(){return[...this.#ie].map((e=>this.#G.getContext(e)))}get allChildren(){const e=this.directChildren;return e.concat(...e.map((e=>e.allChildren)))}isTopLevelContext(){return null===this.#ae}get top(){let e=this,t=e.parent;for(;t;)e=t,t=e.parent;return e}addChild(e){this.#ie.add(e)}#me(){this.directChildren.map((e=>e.delete()))}get#ge(){if(void 0===this.#le)throw new Error(`No default realm for browsing context ${this.#A}`);return this.#le}get cdpTarget(){return this.#ue}updateCdpTarget(e){this.#ue=e,this.#he()}get url(){return this.#de}async awaitLoaded(){await this.#oe.Page.lifecycleEvent.load}awaitUnblocked(){return this.#ue.targetUnblocked}async getOrCreateSandbox(e){if(void 0===e||""===e)return this.#ge;let t=this.#J.findRealms({browsingContextId:this.id,sandbox:e});if(0===t.length&&(await this.#ue.cdpClient.sendCommand("Page.createIsolatedWorld",{frameId:this.id,worldName:e}),t=this.#J.findRealms({browsingContextId:this.id,sandbox:e})),1!==t.length)throw Error(`Sandbox ${e} wasn't created.`);return t[0]}serializeToBidiValue(e=0,t=!0){return{context:this.#A,url:this.url,children:e>0?this.directChildren.map((t=>t.serializeToBidiValue(e-1,!1))):null,...t?{parent:this.#ae}:{}}}onTargetInfoChanged(e){this.#de=e.targetInfo.url,this.#pe&&(this.#U.registerEvent({method:ge.BrowsingContext.EventNames.NavigationStarted,params:{context:this.id,navigation:null,timestamp:xe.getTimestamp(),url:this.#de}},this.id),this.#pe=!1)}#he(){this.#ue.cdpClient.on("Page.frameNavigated",(e=>{if(this.id!==e.frame.id)return;const t=xe.getTimestamp();this.#de=e.frame.url+(e.frame.urlFragment??""),this.#me(),this.#U.registerEvent({method:ge.BrowsingContext.EventNames.FragmentNavigated,params:{context:this.id,navigation:this.#ce??null,timestamp:t,url:this.#de}},this.id)})),this.#ue.cdpClient.on("Page.navigatedWithinDocument",(e=>{if(this.id!==e.frameId)return;const t=xe.getTimestamp();this.#de=e.url,this.#oe.Page.navigatedWithinDocument.resolve(e),this.#U.registerEvent({method:ge.BrowsingContext.EventNames.FragmentNavigated,params:{context:this.id,navigation:null,timestamp:t,url:this.#de}},this.id)})),this.#ue.cdpClient.on("Page.frameStartedLoading",(e=>{this.id===e.frameId&&(this.#pe=!0)})),this.#ue.cdpClient.on("Page.frameStoppedLoading",(e=>{this.id===e.frameId&&(this.#pe=!1)})),this.#ue.cdpClient.on("Page.lifecycleEvent",(e=>{if(this.id!==e.frameId)return;if("init"===e.name)return this.#fe(e.loaderId),void this.#oe.documentInitialized.resolve();if("commit"===e.name)return void(this.#ce=e.loaderId);if(e.loaderId!==this.#ce)return;const t=xe.getTimestamp();switch(e.name){case"DOMContentLoaded":this.#oe.Page.lifecycleEvent.DOMContentLoaded.resolve(e),this.#U.registerEvent({method:ge.BrowsingContext.EventNames.DomContentLoadedEvent,params:{context:this.id,navigation:this.#ce??null,timestamp:t,url:this.#de}},this.id);break;case"load":this.#oe.Page.lifecycleEvent.load.resolve(e),this.#U.registerEvent({method:ge.BrowsingContext.EventNames.LoadEvent,params:{context:this.id,navigation:this.#ce??null,timestamp:t,url:this.#de}},this.id)}})),this.#ue.cdpClient.on("Runtime.executionContextCreated",(e=>{if(e.context.auxData.frameId!==this.id)return;if(!["default","isolated"].includes(e.context.auxData.type))return;const t=new ye.Realm(this.#J,this.#G,e.context.uniqueId,this.id,e.context.id,this.#ve(e),"window","isolated"===e.context.auxData.type?e.context.name:void 0,this.#ue.cdpSessionId,this.#ue.cdpClient,this.#U,this.#t);e.context.auxData.isDefault&&(this.#le=t,Promise.all(this.#ue.getChannels(this.id).map((e=>e.startListenerFromWindow(t,this.#U)))))})),this.#ue.cdpClient.on("Runtime.executionContextDestroyed",(e=>{this.#J.deleteRealms({cdpSessionId:this.#ue.cdpSessionId,executionContextId:e.executionContextId})})),this.#ue.cdpClient.on("Runtime.executionContextsCleared",(()=>{this.#J.deleteRealms({cdpSessionId:this.#ue.cdpSessionId})}))}#ve(e){return"isolated"===e.context.auxData.type?this.#ge.origin:["://",""].includes(e.context.origin)?"null":e.context.origin}#fe(e){void 0!==e&&this.#ce!==e?(this.#ye(),this.#ce=e):this.#oe.Page.navigatedWithinDocument.isFinished?this.#oe.Page.navigatedWithinDocument=new ve.Deferred:this.#t?.(fe.LogType.browsingContexts,"Document changed (navigatedWithinDocument)")}#ye(){this.#oe.documentInitialized.isFinished?this.#oe.documentInitialized=new ve.Deferred:this.#t?.(fe.LogType.browsingContexts,"Document changed (document initialized)"),this.#oe.Page.lifecycleEvent.DOMContentLoaded.isFinished?this.#oe.Page.lifecycleEvent.DOMContentLoaded=new ve.Deferred:this.#t?.(fe.LogType.browsingContexts,"Document changed (DOMContentLoaded)"),this.#oe.Page.lifecycleEvent.load.isFinished?this.#oe.Page.lifecycleEvent.load=new ve.Deferred:this.#t?.(fe.LogType.browsingContexts,"Document changed (load)")}async navigate(e,t){await this.awaitUnblocked();const r=await this.#ue.cdpClient.sendCommand("Page.navigate",{url:e,frameId:this.id});if(r.errorText)throw new ge.Message.UnknownErrorException(r.errorText);switch(this.#fe(r.loaderId),t){case"none":break;case"interactive":void 0===r.loaderId?await this.#oe.Page.navigatedWithinDocument:await this.#oe.Page.lifecycleEvent.DOMContentLoaded;break;case"complete":void 0===r.loaderId?await this.#oe.Page.navigatedWithinDocument:await this.awaitLoaded()}return{result:{navigation:r.loaderId??null,url:e}}}async reload(e,t){switch(await this.awaitUnblocked(),await this.#ue.cdpClient.sendCommand("Page.reload",{ignoreCache:e}),this.#ye(),t){case"none":break;case"interactive":await this.#oe.Page.lifecycleEvent.DOMContentLoaded;break;case"complete":await this.awaitLoaded()}return{result:{}}}async setViewport(e){if(null===e)await this.#ue.cdpClient.sendCommand("Emulation.clearDeviceMetricsOverride");else try{await this.#ue.cdpClient.sendCommand("Emulation.setDeviceMetricsOverride",{width:e.width,height:e.height,deviceScaleFactor:0,mobile:!1,dontSetVisibleSize:!0})}catch(e){if(e.message.startsWith("Width and height values must be positive"))throw new ge.Message.UnsupportedOperationException("Provided viewport dimensions are not supported");throw e}}async captureScreenshot(){let e;if(await this.#ue.cdpClient.sendCommand("Page.bringToFront"),this.isTopLevelContext()){const{cssContentSize:t,cssLayoutViewport:r}=await this.#ue.cdpClient.sendCommand("Page.getLayoutMetrics");e={x:t.x,y:t.y,width:r.clientWidth,height:r.clientHeight}}else{const{result:{value:t}}=await this.#ue.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((()=>{const e=globalThis.document.documentElement.getBoundingClientRect();return JSON.stringify({x:e.x,y:e.y,width:e.width,height:e.height})})),executionContextId:this.#ge.executionContextId});e=JSON.parse(t)}return{result:{data:(await this.#ue.cdpClient.sendCommand("Page.captureScreenshot",{clip:{...e,scale:1}})).data}}}async print(e){const t={};void 0!==e.background&&(t.printBackground=e.background),void 0!==e.margin?.bottom&&(t.marginBottom=(0,me.inchesFromCm)(e.margin.bottom)),void 0!==e.margin?.left&&(t.marginLeft=(0,me.inchesFromCm)(e.margin.left)),void 0!==e.margin?.right&&(t.marginRight=(0,me.inchesFromCm)(e.margin.right)),void 0!==e.margin?.top&&(t.marginTop=(0,me.inchesFromCm)(e.margin.top)),void 0!==e.orientation&&(t.landscape="landscape"===e.orientation),void 0!==e.page?.height&&(t.paperHeight=(0,me.inchesFromCm)(e.page.height)),void 0!==e.page?.width&&(t.paperWidth=(0,me.inchesFromCm)(e.page.width)),void 0!==e.pageRanges&&(t.pageRanges=e.pageRanges.join(",")),void 0!==e.scale&&(t.scale=e.scale),void 0!==e.shrinkToFit&&(t.preferCSSPageSize=!e.shrinkToFit);return{result:{data:(await this.#ue.cdpClient.sendCommand("Page.printToPDF",t)).data}}}}ee.BrowsingContextImpl=xe;var we={},be={},Ce={};Object.defineProperty(Ce,"__esModule",{value:!0}),Ce.getRemoteValuesText=Ce.logMessageFormatter=void 0;const Ie=["%s","%d","%i","%f","%o","%O","%c"];function _e(e){return Ie.some((t=>e.includes(t)))}function Se(e){let t="";const r=e[0].value.toString(),n=e.slice(1,void 0),s=r.split(new RegExp(Ie.map((e=>`(${e})`)).join("|"),"g"));for(const r of s)if(void 0!==r&&""!==r)if(_e(r)){const s=n.shift();if(void 0===s)throw new Error(`Less value is provided: "${Ee(e,!1)}"`);"%s"===r?t+=Pe(s):"%d"===r||"%i"===r?"bigint"===s.type||"number"===s.type||"string"===s.type?t+=parseInt(s.value.toString(),10):t+="NaN":"%f"===r?"bigint"===s.type||"number"===s.type||"string"===s.type?t+=parseFloat(s.value.toString()):t+="NaN":t+=Te(s)}else t+=r;if(n.length>0)throw new Error(`More value is provided: "${Ee(e,!1)}"`);return t}function Te(e){if("array"!==e.type&&"bigint"!==e.type&&"date"!==e.type&&"number"!==e.type&&"object"!==e.type&&"string"!==e.type)return Pe(e);if("bigint"===e.type)return`${e.value.toString()}n`;if("number"===e.type)return e.value.toString();if(["date","string"].includes(e.type))return JSON.stringify(e.value);if("object"===e.type)return`{${e.value.map((e=>`${JSON.stringify(e[0])}:${Te(e[1])}`)).join(",")}}`;if("array"===e.type)return`[${e.value?.map((e=>Te(e))).join(",")??""}]`;throw Error(`Invalid value type: ${e.toString()}`)}function Pe(e){if(!Object.hasOwn(e,"value"))return e.type;switch(e.type){case"string":case"number":case"boolean":case"bigint":return String(e.value);case"regexp":return`/${e.value.pattern}/${e.value.flags??""}`;case"date":return new Date(e.value).toString();case"object":return`Object(${e.value?.length??""})`;case"array":return`Array(${e.value?.length??""})`;case"map":return`Map(${e.value.length})`;case"set":return`Set(${e.value.length})`;case"node":return"node";default:return e.type}}function Ee(e,t){const r=e[0];return r?"string"===r.type&&_e(r.value.toString())&&t?Se(e):e.map((e=>Pe(e))).join(" "):""}Ce.logMessageFormatter=Se,Ce.getRemoteValuesText=Ee,Object.defineProperty(be,"__esModule",{value:!0}),be.LogManager=void 0;const ke=b,Ne=Ce;function Me(e){const t=e?.callFrames.map((e=>({columnNumber:e.columnNumber,functionName:e.functionName,lineNumber:e.lineNumber,url:e.url})));return t?{callFrames:t}:void 0}class Oe{#U;#J;#ue;constructor(e,t,r){this.#ue=e,this.#J=t,this.#U=r}static create(e,t,r){const n=new Oe(e,t,r);return n.#xe(),n}#xe(){this.#we()}#we(){this.#ue.cdpClient.on("Runtime.consoleAPICalled",(e=>{const t=this.#J.findRealm({cdpSessionId:this.#ue.cdpSessionId,executionContextId:e.executionContextId}),r=void 0===t?Promise.resolve(e.args):Promise.all(e.args.map((e=>t.serializeCdpObject(e,"none"))));this.#U.registerPromiseEvent(r.then((r=>{return{method:ke.Log.EventNames.LogEntryAddedEvent,params:{level:(n=e.type,["assert","error"].includes(n)?"error":["debug","trace"].includes(n)?"debug":["warn","warning"].includes(n)?"warn":"info"),source:{realm:t?.realmId??"UNKNOWN",context:t?.browsingContextId??"UNKNOWN"},text:(0,Ne.getRemoteValuesText)(r,!0),timestamp:Math.round(e.timestamp),stackTrace:Me(e.stackTrace),type:"console",method:"warning"===e.type?"warn":e.type,args:r}};var n})),t?.browsingContextId??"UNKNOWN",ke.Log.EventNames.LogEntryAddedEvent)})),this.#ue.cdpClient.on("Runtime.exceptionThrown",(e=>{const t=this.#J.findRealm({cdpSessionId:this.#ue.cdpSessionId,executionContextId:e.exceptionDetails.executionContextId}),r=(async()=>e.exceptionDetails.exception?void 0===t?JSON.stringify(e.exceptionDetails.exception):t.stringifyObject(e.exceptionDetails.exception):e.exceptionDetails.text)();this.#U.registerPromiseEvent(r.then((r=>({method:ke.Log.EventNames.LogEntryAddedEvent,params:{level:"error",source:{realm:t?.realmId??"UNKNOWN",context:t?.browsingContextId??"UNKNOWN"},text:r,timestamp:Math.round(e.timestamp),stackTrace:Me(e.exceptionDetails.stackTrace),type:"javascript"}}))),t?.browsingContextId??"UNKNOWN",ke.Log.EventNames.LogEntryAddedEvent)}))}}be.LogManager=Oe;var De={},Ze={};Object.defineProperty(Ze,"__esModule",{value:!0}),Ze.DefaultMap=void 0;class Re extends Map{#be;constructor(e,t){super(t),this.#be=e}get(e){return this.has(e)||this.set(e,this.#be(e)),super.get(e)}}Ze.DefaultMap=Re;var ze={};Object.defineProperty(ze,"__esModule",{value:!0}),ze.NetworkRequest=void 0;const Ae=re,je=b;class Le{static#Ce="UNKNOWN";requestId;#Ie=!1;#_e;#U;#Se;#Te;#Pe;#Ee;#ke=new Ae.Deferred;#Ne=new Ae.Deferred;constructor(e,t){this.requestId=e,this.#_e=0,this.#U=t}onRequestWillBeSentEvent(e){void 0===this.#Se&&(this.#Se=e,void 0!==this.#Te&&this.#ke.resolve(),this.#Me())}onRequestWillBeSentExtraInfoEvent(e){void 0===this.#Te&&(this.#Te=e,void 0!==this.#Se&&this.#ke.resolve())}onResponseReceivedEventExtraInfo(e){void 0===this.#Ee&&(this.#Ee=e,void 0!==this.#Pe&&this.#Ne.resolve())}onResponseReceivedEvent(e){void 0===this.#Pe&&(this.#Pe=e,e.hasExtraInfo||this.#ke.isFinished||this.#ke.resolve(),e.hasExtraInfo&&void 0===this.#Ee&&!this.#Ie||this.#Ne.resolve(),this.#Oe())}onServedFromCache(){void 0!==this.#Se&&this.#ke.resolve(),void 0!==this.#Pe&&this.#Ne.resolve(),this.#Ie=!0}onLoadingFailedEvent(e){this.#ke.resolve(),this.#Ne.reject(e),this.#U.registerEvent({method:je.Network.EventNames.FetchErrorEvent,params:{...this.#De(),errorText:e.errorText}},this.#Se?.frameId??null)}#De(){return{context:this.#Se?.frameId??null,navigation:this.#Ze(),redirectCount:this.#_e,request:this.#Re(),timestamp:Math.round(1e3*(this.#Se?.wallTime??0))}}#Ze(){return this.#Se&&this.#Se.loaderId&&this.#Se.loaderId===this.#Se.requestId?this.#Se.loaderId:null}#Re(){const e=this.#Te?Le.#ze(this.#Te.associatedCookies):[];return{request:this.#Se?.requestId??Le.#Ce,url:this.#Se?.request.url??Le.#Ce,method:this.#Se?.request.method??Le.#Ce,headers:Le.#Ae(this.#Se?.request.headers),cookies:e,headersSize:-1,bodySize:0,timings:{timeOrigin:0,requestTime:0,redirectStart:0,redirectEnd:0,fetchStart:0,dnsStart:0,dnsEnd:0,connectStart:0,connectEnd:0,tlsStart:0,requestStart:0,responseStart:0,responseEnd:0}}}#Me(){this.#je()||this.#U.registerPromiseEvent(this.#ke.then((()=>this.#Le())),this.#Se?.frameId??null,je.Network.EventNames.BeforeRequestSentEvent)}#Le(){if(void 0===this.#Se)throw new Error("RequestWillBeSentEvent is not set");return{method:je.Network.EventNames.BeforeRequestSentEvent,params:{...this.#De(),initiator:{type:Le.#Be(this.#Se.initiator.type)}}}}#Oe(){this.#je()||this.#U.registerPromiseEvent(this.#Ne.then((()=>this.#Ke())),this.#Pe?.frameId??null,je.Network.EventNames.ResponseCompletedEvent)}#Ke(){if(void 0===this.#Se)throw new Error("RequestWillBeSentEvent is not set");if(void 0===this.#Pe)throw new Error("ResponseReceivedEvent is not set");this.#Pe.response.fromDiskCache&&(this.#Ee=void 0);const e=Le.#Ae(this.#Pe.response.headers);return{method:je.Network.EventNames.ResponseCompletedEvent,params:{...this.#De(),response:{url:this.#Pe.response.url,protocol:this.#Pe.response.protocol??"",status:this.#Ee?.statusCode??this.#Pe.response.status,statusText:this.#Pe.response.statusText,fromCache:this.#Pe.response.fromDiskCache||this.#Pe.response.fromPrefetchCache||this.#Ie,headers:e,mimeType:this.#Pe.response.mimeType,bytesReceived:this.#Pe.response.encodedDataLength,headersSize:this.#Fe(e),bodySize:0,content:{size:0}}}}}#Fe(e){return e.reduce(((e,t)=>e+t.name.length+(t.value?.length??0)+4),0)}#je(){return this.#Se?.request.url.endsWith("/favicon.ico")??!1}static#Ae(e){return e?Object.entries(e).map((([e,t])=>({name:e,value:t}))):[]}static#Be(e){switch(e){case"parser":case"script":case"preflight":return e;default:return"other"}}static#ze(e){return e.map((e=>({name:e.cookie.name,value:e.cookie.value,domain:e.cookie.domain,path:e.cookie.path,expires:e.cookie.expires,size:e.cookie.size,httpOnly:e.cookie.httpOnly,secure:e.cookie.secure,sameSite:Le.#Ue(e.cookie.sameSite)})))}static#Ue(e){switch(e){case"Strict":return"strict";case"Lax":return"lax";default:return"none"}}}ze.NetworkRequest=Le,Object.defineProperty(De,"__esModule",{value:!0}),De.NetworkProcessor=void 0;const Be=Ze,Ke=ze;class Fe{#U;#Ve;constructor(e){this.#U=e,this.#Ve=new Be.DefaultMap((e=>new Ke.NetworkRequest(e,this.#U)))}static async create(e,t){const r=new Fe(t);return e.on("Network.requestWillBeSent",(e=>{r.#$e(e.requestId).onRequestWillBeSentEvent(e)})),e.on("Network.requestWillBeSentExtraInfo",(e=>{r.#$e(e.requestId).onRequestWillBeSentExtraInfoEvent(e)})),e.on("Network.responseReceived",(e=>{r.#$e(e.requestId).onResponseReceivedEvent(e)})),e.on("Network.responseReceivedExtraInfo",(e=>{r.#$e(e.requestId).onResponseReceivedEventExtraInfo(e)})),e.on("Network.loadingFailed",(e=>{r.#$e(e.requestId).onLoadingFailedEvent(e)})),e.on("Network.requestServedFromCache",(e=>{r.#$e(e.requestId).onServedFromCache()})),await e.sendCommand("Network.enable"),r}#$e(e){return this.#Ve.get(e)}}De.NetworkProcessor=Fe,Object.defineProperty(we,"__esModule",{value:!0}),we.CdpTarget=void 0;const Ue=be,Ve=re,$e=De;class qe{#qe;#We;#re;#He;#U;#Je;#Ge;#Xe;static create(e,t,r,n,s,a,i){const o=new qe(e,t,r,n,a,i);return Ue.LogManager.create(o,s,a),o.#Ye(),o.#Qe(),o}constructor(e,t,r,n,s,a){this.#qe=e,this.#We=t,this.#re=r,this.#He=n,this.#U=s,this.#Je=a,this.#Xe=!1,this.#Ge=new Ve.Deferred}get targetUnblocked(){return this.#Ge}get targetId(){return this.#qe}get cdpClient(){return this.#re}get cdpSessionId(){return this.#He}async#Qe(){try{this.#U.isNetworkDomainEnabled&&await this.enableNetworkDomain(),await this.#re.sendCommand("Runtime.enable"),await this.#re.sendCommand("Page.enable"),await this.#re.sendCommand("Page.setLifecycleEventsEnabled",{enabled:!0}),await this.#re.sendCommand("Target.setAutoAttach",{autoAttach:!0,waitForDebuggerOnStart:!0,flatten:!0}),await this.#et(),await this.#re.sendCommand("Runtime.runIfWaitingForDebugger")}catch(e){if(!this.#re.isCloseError(e))throw e}this.#Ge.resolve()}async enableNetworkDomain(){this.#Xe||(this.#Xe=!0,await $e.NetworkProcessor.create(this.cdpClient,this.#U))}#Ye(){this.#re.on("*",((e,t)=>{"string"==typeof e&&this.#U.registerEvent({method:`cdp.${e}`,params:{event:e,params:t,session:this.#He}},null)}))}getChannels(e){return this.#Je.findPreloadScripts({contextIds:[null,e]}).flatMap((e=>e.channels))}async#et(){for(const e of this.#Je.findPreloadScripts({contextIds:[null,this.#We]}))await e.initInTarget(this),e.scheduleEvaluateInTarget(this)}}we.CdpTarget=qe;var We={};Object.defineProperty(We,"__esModule",{value:!0}),We.BidiPreloadScript=void 0;const He=oe,Je=ie;We.BidiPreloadScript=class{#A=(0,He.uuidv4)();#tt=[];#rt;#nt;#st=new Set;#at;get id(){return this.#A}get contextId(){return this.#nt}get targetIds(){return this.#st}constructor(e){if(void 0!==e.sandbox)throw new Error("Sandbox is not supported yet");this.#at=e.arguments?.map((e=>new Je.ChannelProxy(e.value)))??[],this.#rt=e.functionDeclaration,this.#nt=e.context??null}get channels(){return this.#at}async initInTargets(e){await Promise.all(Array.from(e).map((e=>this.initInTarget(e))))}#it(){const e=`[${this.channels.map((e=>e.getEvalInWindowStr())).join(", ")}]`;return`(()=>{(${this.#rt})(...${e})})()`}async initInTarget(e){const t=await e.cdpClient.sendCommand("Page.addScriptToEvaluateOnNewDocument",{source:this.#it()});this.#tt.push({target:e,preloadScriptId:t.identifier}),this.#st.add(e.targetId)}scheduleEvaluateInTarget(e){e.cdpClient.sendCommand("Runtime.evaluate",{expression:this.#it()})}async remove(){for(const e of this.#tt){const t=e.target,r=e.preloadScriptId;await t.cdpClient.sendCommand("Page.removeScriptToEvaluateOnNewDocument",{identifier:r})}}cdpTargetIsGone(e){this.#tt=this.#tt.filter((t=>t.target?.targetId!==e)),this.#st.delete(e)}},Object.defineProperty(M,"__esModule",{value:!0}),M.BrowsingContextProcessor=void 0;const Ge=b,Xe=P,Ye=O,Qe=F,et=Q,tt=ee,rt=we,nt=We;M.BrowsingContextProcessor=class{#G;#ot;#U;#t;#J;#dt;#Je=new et.PreloadScriptStorage;#ct=new Ye.InputStateManager;constructor(e,t,r,n,s,a){this.#ot=e,this.#dt=t,this.#U=r,this.#G=n,this.#J=s,this.#t=a,this.#Ye(this.#ot.browserClient())}#Ye(e){e.on("Target.attachedToTarget",(t=>{this.#ut(t,e)})),e.on("Target.detachedFromTarget",(e=>{this.#lt(e)})),e.on("Target.targetInfoChanged",(e=>{this.#pt(e)})),e.on("Page.frameAttached",(e=>{this.#ht(e)})),e.on("Page.frameDetached",(e=>{this.#mt(e)}))}#ht(e){const t=this.#G.findContext(e.parentFrameId);void 0!==t&&tt.BrowsingContextImpl.create(t.cdpTarget,this.#J,e.frameId,e.parentFrameId,this.#U,this.#G,this.#t)}#mt(e){"swap"!==e.reason&&this.#G.findContext(e.frameId)?.delete()}#ut(e,t){const{sessionId:r,targetInfo:n}=e,s=this.#ot.getCdpClient(r);if(!this.#gt(n))return void s.sendCommand("Runtime.runIfWaitingForDebugger").then((()=>t.sendCommand("Target.detachFromTarget",e))).catch((e=>this.#t?.(Xe.LogType.system,e)));this.#t?.(Xe.LogType.browsingContexts,"AttachedToTarget event received:",JSON.stringify(e,null,2)),this.#Ye(s);const a=this.#G.findContext(n.targetId),i=rt.CdpTarget.create(n.targetId,a?.parentId??null,s,r,this.#J,this.#U,this.#Je);a?a.updateCdpTarget(i):tt.BrowsingContextImpl.create(i,this.#J,n.targetId,null,this.#U,this.#G,this.#t)}#lt(e){const t=e.targetId;this.#G.findContext(t)?.delete(),this.#Je.findPreloadScripts({targetId:t}).map((e=>e.cdpTargetIsGone(t)))}#pt(e){const t=e.targetInfo.targetId;this.#G.findContext(t)?.onTargetInfoChanged(e)}async#ft(e){if("realm"in e)return this.#J.getRealm({realmId:e.realm});return this.#G.getContext(e.context).getOrCreateSandbox(e.sandbox)}process_browsingContext_getTree(e){return{result:{contexts:(void 0===e.root?this.#G.getTopLevelContexts():[this.#G.getContext(e.root)]).map((t=>t.serializeToBidiValue(e.maxDepth??Number.MAX_VALUE)))}}}async process_browsingContext_create(e){const t=this.#ot.browserClient();let r,n;if(void 0!==e.referenceContext&&(r=this.#G.getContext(e.referenceContext),!r.isTopLevelContext()))throw new Ge.Message.InvalidArgumentException("referenceContext should be a top-level context");switch(e.type){case"tab":n=await t.sendCommand("Target.createTarget",{url:"about:blank",newWindow:!1});break;case"window":n=await t.sendCommand("Target.createTarget",{url:"about:blank",newWindow:!0})}const s=n.targetId,a=this.#G.getContext(s);return await a.awaitLoaded(),{result:{context:a.id}}}process_browsingContext_navigate(e){return this.#G.getContext(e.context).navigate(e.url,e.wait??"none")}process_browsingContext_reload(e){return this.#G.getContext(e.context).reload(e.ignoreCache??!1,e.wait??"none")}async process_browsingContext_captureScreenshot(e){return this.#G.getContext(e.context).captureScreenshot()}async process_browsingContext_print(e){return this.#G.getContext(e.context).print(e)}async process_script_addPreloadScript(e){const t=new nt.BidiPreloadScript(e);this.#Je.addPreloadScript(t);const r=new Set(void 0===e.context||null===e.context?this.#G.getTopLevelContexts().map((e=>e.cdpTarget)):[this.#G.getContext(e.context).cdpTarget]);return await t.initInTargets(r),{result:{script:t.id}}}async process_script_removePreloadScript(e){const t=e.script,r=this.#Je.findPreloadScripts({id:t});if(0===r.length)throw new Ge.Message.NoSuchScriptException(`No preload script with BiDi ID '${t}'`);return await Promise.all(r.map((e=>e.remove()))),this.#Je.removeBiDiPreloadScripts({id:t}),{result:{}}}async process_script_evaluate(e){return(await this.#ft(e.target)).scriptEvaluate(e.expression,e.awaitPromise,e.resultOwnership??"none",e.serializationOptions??{})}process_script_getRealms(e){void 0!==e.context&&this.#G.getContext(e.context);const t=this.#J.findRealms({browsingContextId:e.context,type:e.type}).map((e=>e.toBiDi()));return{result:{realms:t}}}async process_script_callFunction(e){return(await this.#ft(e.target)).callFunction(e.functionDeclaration,e.this??{type:"undefined"},e.arguments??[],e.awaitPromise,e.resultOwnership??"none",e.serializationOptions??{})}async process_script_disown(e){const t=await this.#ft(e.target);return await Promise.all(e.handles.map((async e=>t.disown(e)))),{result:{}}}async process_input_performActions(e){const t=this.#G.getContext(e.context),r=this.#ct.get(t.top),n=this.#vt(e,r),s=new Qe.ActionDispatcher(r,t,await Qe.ActionDispatcher.isMacOS(t).catch((()=>!1)));return await s.dispatchActions(n),{result:{}}}#vt(e,t){const r=[];for(const n of e.actions){switch(n.type){case Ge.Input.SourceActionsType.Pointer:{n.parameters??={pointerType:Ge.Input.PointerType.Mouse},n.parameters.pointerType??=Ge.Input.PointerType.Mouse;const e=t.getOrCreate(n.id,Ge.Input.SourceActionsType.Pointer,n.parameters.pointerType);if(e.subtype!==n.parameters.pointerType)throw new Ge.Message.InvalidArgumentException(`Expected input source ${n.id} to be ${e.subtype}; got ${n.parameters.pointerType}.`);break}default:t.getOrCreate(n.id,n.type)}const e=n.actions.map((e=>({id:n.id,action:e})));for(let t=0;t<e.length;t++)r.length===t&&r.push([]),r[t].push(e[t])}return r}async process_input_releaseActions(e){const t=this.#G.getContext(e.context),r=t.top,n=this.#ct.get(r),s=new Qe.ActionDispatcher(n,t,await Qe.ActionDispatcher.isMacOS(t).catch((()=>!1)));return await s.dispatchTickActions(n.cancelList.reverse()),this.#ct.delete(r),{result:{}}}async process_browsingContext_setViewport(e){const t=this.#G.getContext(e.context);if(!t.isTopLevelContext())throw new Ge.Message.InvalidArgumentException("Emulating viewport is only supported on the top-level context");return await t.setViewport(e.viewport),{result:{}}}async process_browsingContext_close(e){const t=this.#ot.browserClient();if(!this.#G.getContext(e.context).isTopLevelContext())throw new Ge.Message.InvalidArgumentException("A top-level browsing context cannot be closed.");const r=new Promise((r=>{const n=s=>{s.targetId===e.context&&(t.off("Target.detachedFromTarget",n),r())};t.on("Target.detachedFromTarget",n)}));return await t.sendCommand("Target.closeTarget",{targetId:e.context}),await r,{result:{}}}#gt(e){return e.targetId!==this.#dt&&["page","iframe"].includes(e.type)}async process_cdp_sendCommand(e){const t=e.session?this.#ot.getCdpClient(e.session):this.#ot.browserClient();return{result:await t.sendCommand(e.method,e.params),session:e.session}}process_cdp_getSession(e){const t=e.context,r=this.#G.getContext(t).cdpTarget.cdpSessionId;return void 0===r?{result:{session:null}}:{result:{session:r}}}};var st={};Object.defineProperty(st,"__esModule",{value:!0}),st.OutgoingBidiMessage=void 0;class at{#yt;#xt;constructor(e,t){this.#yt=e,this.#xt=t}static async createFromPromise(e,t){return e.then((e=>new at(e,t)))}static createResolved(e,t){return Promise.resolve(new at(e,t))}get message(){return this.#yt}get channel(){return this.#xt}}st.OutgoingBidiMessage=at,Object.defineProperty(N,"__esModule",{value:!0}),N.CommandProcessor=void 0;const it=b,ot=P,dt=_,ct=M,ut=st;class lt{parseAddPreloadScriptParams(e){return e}parseRemovePreloadScriptParams(e){return e}parseGetRealmsParams(e){return e}parseCallFunctionParams(e){return e}parseEvaluateParams(e){return e}parseDisownParams(e){return e}parseSendCommandParams(e){return e}parseGetSessionParams(e){return e}parseSubscribeParams(e){return e}parseNavigateParams(e){return e}parseReloadParams(e){return e}parseGetTreeParams(e){return e}parseCreateParams(e){return e}parseCloseParams(e){return e}parseCaptureScreenshotParams(e){return e}parsePrintParams(e){return e}parsePerformActionsParams(e){return e}parseReleaseActionsParams(e){return e}parseSetViewportParams(e){return e}}class pt extends dt.EventEmitter{#wt;#U;#bt;#t;constructor(e,t,r,n=new lt,s,a,i){super(),this.#U=t,this.#t=i,this.#wt=new ct.BrowsingContextProcessor(e,r,t,s,a,i),this.#bt=n}static#Ct(){return{result:{ready:!1,message:"already connected"}}}async#It(e,t){return await this.#U.subscribe(e.events,e.contexts??[null],t),{result:{}}}async#_t(e,t){return await this.#U.unsubscribe(e.events,e.contexts??[null],t),{result:{}}}async#St(e){switch(e.method){case"session.status":return pt.#Ct();case"session.subscribe":return this.#It(this.#bt.parseSubscribeParams(e.params),e.channel??null);case"session.unsubscribe":return this.#_t(this.#bt.parseSubscribeParams(e.params),e.channel??null);case"browsingContext.create":return this.#wt.process_browsingContext_create(this.#bt.parseCreateParams(e.params));case"browsingContext.close":return this.#wt.process_browsingContext_close(this.#bt.parseCloseParams(e.params));case"browsingContext.getTree":return this.#wt.process_browsingContext_getTree(this.#bt.parseGetTreeParams(e.params));case"browsingContext.navigate":return this.#wt.process_browsingContext_navigate(this.#bt.parseNavigateParams(e.params));case"browsingContext.captureScreenshot":return this.#wt.process_browsingContext_captureScreenshot(this.#bt.parseCaptureScreenshotParams(e.params));case"browsingContext.print":return this.#wt.process_browsingContext_print(this.#bt.parsePrintParams(e.params));case"browsingContext.reload":return this.#wt.process_browsingContext_reload(this.#bt.parseReloadParams(e.params));case"browsingContext.setViewport":return this.#wt.process_browsingContext_setViewport(this.#bt.parseSetViewportParams(e.params));case"script.addPreloadScript":return this.#wt.process_script_addPreloadScript(this.#bt.parseAddPreloadScriptParams(e.params));case"script.removePreloadScript":return this.#wt.process_script_removePreloadScript(this.#bt.parseRemovePreloadScriptParams(e.params));case"script.getRealms":return this.#wt.process_script_getRealms(this.#bt.parseGetRealmsParams(e.params));case"script.callFunction":return this.#wt.process_script_callFunction(this.#bt.parseCallFunctionParams(e.params));case"script.evaluate":return this.#wt.process_script_evaluate(this.#bt.parseEvaluateParams(e.params));case"script.disown":return this.#wt.process_script_disown(this.#bt.parseDisownParams(e.params));case"input.performActions":return this.#wt.process_input_performActions(this.#bt.parsePerformActionsParams(e.params));case"input.releaseActions":return this.#wt.process_input_releaseActions(this.#bt.parseReleaseActionsParams(e.params));case"cdp.sendCommand":return this.#wt.process_cdp_sendCommand(this.#bt.parseSendCommandParams(e.params));case"cdp.getSession":return this.#wt.process_cdp_getSession(this.#bt.parseGetSessionParams(e.params))}throw new it.Message.UnknownCommandException(`Unknown command '${e.method}'.`)}async processCommand(e){try{const t=await this.#St(e),r={id:e.id,...t};this.emit("response",ut.OutgoingBidiMessage.createResolved(r,e.channel??null))}catch(t){if(t instanceof it.Message.ErrorResponse){const r=t;this.emit("response",ut.OutgoingBidiMessage.createResolved(r.toErrorResponse(e.id),e.channel??null))}else{const r=t;this.#t?.(ot.LogType.bidi,r),this.emit("response",ut.OutgoingBidiMessage.createResolved(new it.Message.UnknownErrorException(r.message).toErrorResponse(e.id),e.channel??null))}}}}N.CommandProcessor=pt;var ht={};Object.defineProperty(ht,"__esModule",{value:!0}),ht.BrowsingContextStorage=void 0;const mt=b;ht.BrowsingContextStorage=class{#Tt=new Map;getTopLevelContexts(){return this.getAllContexts().filter((e=>e.isTopLevelContext()))}getAllContexts(){return Array.from(this.#Tt.values())}deleteContextById(e){this.#Tt.delete(e)}deleteContext(e){this.#Tt.delete(e.id)}addContext(e){this.#Tt.set(e.id,e)}hasContext(e){return this.#Tt.has(e)}findContext(e){return this.#Tt.get(e)}findTopLevelContextId(e){if(null===e)return null;const t=this.findContext(e),r=t?.parentId??null;return null===r?e:this.findTopLevelContextId(r)}getContext(e){const t=this.findContext(e);if(void 0===t)throw new mt.Message.NoSuchFrameException(`Context ${e} not found`);return t}};var gt={},ft={};Object.defineProperty(ft,"__esModule",{value:!0}),ft.Buffer=void 0;ft.Buffer=class{#Pt;#Et=[];#kt;constructor(e,t){this.#Pt=e,this.#kt=t}get(){return this.#Et}add(e){for(this.#Et.push(e);this.#Et.length>this.#Pt;){const e=this.#Et.shift();void 0!==e&&this.#kt?.(e)}}};var vt={};Object.defineProperty(vt,"__esModule",{value:!0}),vt.IdWrapper=void 0;class yt{static#Nt=0;#A;constructor(){this.#A=++yt.#Nt}get id(){return this.#A}}vt.IdWrapper=yt;var xt={};Object.defineProperty(xt,"__esModule",{value:!0}),xt.SubscriptionManager=xt.unrollEvents=xt.cartesianProduct=void 0;const wt=b;function bt(...e){return e.reduce(((e,t)=>e.flatMap((e=>t.map((t=>[e,t].flat()))))))}function Ct(e){const t=new Set;function r(e){for(const r of e)t.add(r)}for(const n of e)switch(n){case wt.BrowsingContext.AllEvents:r(Object.values(wt.BrowsingContext.EventNames));break;case wt.Log.AllEvents:r(Object.values(wt.Log.EventNames));break;case wt.Network.AllEvents:r(Object.values(wt.Network.EventNames));break;case wt.Script.AllEvents:r(Object.values(wt.Script.EventNames));break;default:t.add(n)}return[...t.values()]}xt.cartesianProduct=bt,xt.unrollEvents=Ct;xt.SubscriptionManager=class{#Mt=0;#Ot=new Map;#G;constructor(e){this.#G=e}getChannelsSubscribedToEvent(e,t){return Array.from(this.#Ot.keys()).map((r=>({priority:this.#Dt(e,t,r),channel:r}))).filter((({priority:e})=>null!==e)).sort(((e,t)=>e.priority-t.priority)).map((({channel:e})=>e))}#Dt(e,t,r){const n=this.#Ot.get(r);if(void 0===n)return null;const s=this.#G.findTopLevelContextId(t),a=[...new Set([null,s])].map((t=>n.get(t)?.get(e))).filter((e=>void 0!==e));return 0===a.length?null:Math.min(...a)}subscribe(e,t,r){if(t=this.#G.findTopLevelContextId(t),e===wt.BrowsingContext.AllEvents)return void Object.values(wt.BrowsingContext.EventNames).map((e=>this.subscribe(e,t,r)));if(e===wt.Log.AllEvents)return void Object.values(wt.Log.EventNames).map((e=>this.subscribe(e,t,r)));if(e===wt.Network.AllEvents)return void Object.values(wt.Network.EventNames).map((e=>this.subscribe(e,t,r)));if(e===wt.Script.AllEvents)return void Object.values(wt.Script.EventNames).map((e=>this.subscribe(e,t,r)));this.#Ot.has(r)||this.#Ot.set(r,new Map);const n=this.#Ot.get(r);n.has(t)||n.set(t,new Map);const s=n.get(t);s.has(e)||s.set(e,this.#Mt++)}unsubscribeAll(e,t,r){for(const e of t)null!==e&&this.#G.getContext(e);bt(Ct(e),t).map((([e,t])=>this.#Zt(e,t,r))).forEach((e=>e()))}unsubscribe(e,t,r){this.unsubscribeAll([e],[t],r)}#Zt(e,t,r){if(t=this.#G.findTopLevelContextId(t),!this.#Ot.has(r))throw new wt.Message.InvalidArgumentException(`Cannot unsubscribe from ${e}, ${null===t?"null":t}. No subscription found.`);const n=this.#Ot.get(r);if(!n.has(t))throw new wt.Message.InvalidArgumentException(`Cannot unsubscribe from ${e}, ${null===t?"null":t}. No subscription found.`);const s=n.get(t);if(!s.has(e))throw new wt.Message.InvalidArgumentException(`Cannot unsubscribe from ${e}, ${null===t?"null":t}. No subscription found.`);return()=>{s.delete(e),0===s.size&&n.delete(e),0===n.size&&this.#Ot.delete(r)}}},Object.defineProperty(gt,"__esModule",{value:!0}),gt.EventManager=void 0;const It=b,_t=ft,St=vt,Tt=st,Pt=Ze,Et=xt;class kt{#Rt=new St.IdWrapper;#nt;#zt;constructor(e,t){this.#zt=e,this.#nt=t}get id(){return this.#Rt.id}get contextId(){return this.#nt}get event(){return this.#zt}}const Nt=new Map([[It.Log.EventNames.LogEntryAddedEvent,100]]);class Mt{static#At="network";#jt=new Pt.DefaultMap((()=>new Set));#Lt=new Map;#Bt=new Map;#Kt;#Ft;#Ut;constructor(e){this.#Ft=e,this.#Kt=new Et.SubscriptionManager(e.getBrowsingContextStorage()),this.#Ut=!1}get isNetworkDomainEnabled(){return this.#Ut}static#Vt(e,t,r){return JSON.stringify({eventName:e,browsingContext:t,channel:r})}registerEvent(e,t){this.registerPromiseEvent(Promise.resolve(e),t,e.method)}registerPromiseEvent(e,t,r){const n=new kt(e,t),s=this.#Kt.getChannelsSubscribedToEvent(r,t);this.#$t(n,r);for(const t of s)this.#Ft.emitOutgoingMessage(Tt.OutgoingBidiMessage.createFromPromise(e,t)),this.#qt(n,t,r)}async subscribe(e,t,r){for(const e of t)null!==e&&this.#Ft.getBrowsingContextStorage().getContext(e);for(const n of e)for(const e of t){await this.#Wt(n,e),this.#Kt.subscribe(n,e,r);for(const t of this.#Ht(n,e,r))this.#Ft.emitOutgoingMessage(Tt.OutgoingBidiMessage.createFromPromise(t.event,r)),this.#qt(t,r,n)}}async#Wt(e,t){e.startsWith(Mt.#At)&&(null===t?(this.#Ut=!0,await Promise.all(this.#Ft.getBrowsingContextStorage().getAllContexts().map((e=>e.cdpTarget.enableNetworkDomain())))):await this.#Ft.getBrowsingContextStorage().getContext(t).cdpTarget.enableNetworkDomain())}unsubscribe(e,t,r){this.#Kt.unsubscribeAll(e,t,r)}#$t(e,t){if(!Nt.has(t))return;const r=Mt.#Vt(t,e.contextId);this.#Lt.has(r)||this.#Lt.set(r,new _t.Buffer(Nt.get(t))),this.#Lt.get(r).add(e),this.#jt.get(t).add(e.contextId)}#qt(e,t,r){if(!Nt.has(r))return;const n=Mt.#Vt(r,e.contextId,t);this.#Bt.set(n,Math.max(this.#Bt.get(n)??0,e.id))}#Ht(e,t,r){const n=Mt.#Vt(e,t),s=Mt.#Vt(e,t,r),a=this.#Bt.get(s)??-1/0,i=this.#Lt.get(n)?.get().filter((e=>e.id>a))??[];return null===t&&Array.from(this.#jt.get(e).keys()).filter((e=>null!==e&&this.#Ft.getBrowsingContextStorage().hasContext(e))).map((t=>this.#Ht(e,t,r))).forEach((e=>i.push(...e))),i.sort(((e,t)=>e.id-t.id))}}gt.EventManager=Mt;var Ot={};Object.defineProperty(Ot,"__esModule",{value:!0}),Ot.RealmStorage=void 0;const Dt=b;Ot.RealmStorage=class{#Jt=new Map;#Gt=new Map;get knownHandlesToRealm(){return this.#Jt}addRealm(e){this.#Gt.set(e.realmId,e)}findRealms(e){return Array.from(this.#Gt.values()).filter((t=>(void 0===e.realmId||e.realmId===t.realmId)&&((void 0===e.browsingContextId||e.browsingContextId===t.browsingContextId)&&((void 0===e.navigableId||e.navigableId===t.navigableId)&&((void 0===e.executionContextId||e.executionContextId===t.executionContextId)&&((void 0===e.origin||e.origin===t.origin)&&((void 0===e.type||e.type===t.type)&&((void 0===e.sandbox||e.sandbox===t.sandbox)&&(void 0===e.cdpSessionId||e.cdpSessionId===t.cdpSessionId)))))))))}findRealm(e){const t=this.findRealms(e);if(1===t.length)return t[0]}getRealm(e){const t=this.findRealm(e);if(void 0===t)throw new Dt.Message.NoSuchFrameException(`Realm ${JSON.stringify(e)} not found`);return t}deleteRealms(e){this.findRealms(e).map((e=>{e.delete(),this.#Gt.delete(e.realmId),Array.from(this.knownHandlesToRealm.entries()).filter((([,t])=>t===e.realmId)).map((([e])=>this.knownHandlesToRealm.delete(e)))}))}},Object.defineProperty(I,"__esModule",{value:!0}),I.BidiServer=void 0;const Zt=_,Rt=P,zt=E,At=N,jt=ht,Lt=gt,Bt=Ot;class Kt extends Zt.EventEmitter{#Xt;#Yt;#Qt;#G=new jt.BrowsingContextStorage;#J=new Bt.RealmStorage;#t;#er=e=>{this.#Qt.processCommand(e).catch((e=>{this.#t?.(Rt.LogType.system,e)}))};#tr=async e=>{const t=e.message;null!==e.channel&&(t.channel=e.channel),await this.#Yt.sendMessage(t)};constructor(e,t,r,n,s){super(),this.#t=s,this.#Xt=new zt.ProcessingQueue(this.#tr,this.#t),this.#Yt=e,this.#Yt.setOnMessage(this.#er),this.#Qt=new At.CommandProcessor(t,new Lt.EventManager(this),r,n,this.#G,this.#J,this.#t),this.#Qt.on("response",(e=>{this.emitOutgoingMessage(e)}))}static async createAndStart(e,t,r,n,s){const a=new Kt(e,t,r,n,s),i=t.browserClient();return await i.sendCommand("Target.setDiscoverTargets",{discover:!0}),await i.sendCommand("Target.setAutoAttach",{autoAttach:!0,waitForDebuggerOnStart:!0,flatten:!0}),await a.topLevelContextsLoaded(),a}async topLevelContextsLoaded(){await Promise.all(this.#G.getTopLevelContexts().map((e=>e.awaitLoaded())))}emitOutgoingMessage(e){this.#Xt.add(e)}close(){this.#Yt.close()}getBrowsingContextStorage(){return this.#G}}I.BidiServer=Kt,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.OutgoingBidiMessage=e.EventEmitter=e.BidiServer=void 0;var t=I;Object.defineProperty(e,"BidiServer",{enumerable:!0,get:function(){return t.BidiServer}});var r=_;Object.defineProperty(e,"EventEmitter",{enumerable:!0,get:function(){return r.EventEmitter}});var n=st;Object.defineProperty(e,"OutgoingBidiMessage",{enumerable:!0,get:function(){return n.OutgoingBidiMessage}})}(C);var Ft={},Ut={};Object.defineProperty(Ut,"__esModule",{value:!0}),Ut.CdpClient=Ut.CloseError=void 0;const Vt=_;class $t extends Error{}Ut.CloseError=$t;class qt extends Vt.EventEmitter{#ot;#rr;constructor(e,t){super(),this.#ot=e,this.#rr=t}sendCommand(e,...t){return this.#ot.sendCommand(e,t[0],this.#rr)}isCloseError(e){return e instanceof $t}}Ut.CdpClient=qt,Object.defineProperty(Ft,"__esModule",{value:!0}),Ft.CdpConnection=void 0;const Wt=P,Ht=Ut;Ft.CdpConnection=class{#Yt;#nr;#sr=new Map;#ar=new Map;#t;#ir=0;constructor(e,t){this.#Yt=e,this.#t=t,this.#Yt.setOnMessage(this.#or),this.#nr=new Ht.CdpClient(this,void 0)}close(){this.#Yt.close();for(const[,{reject:e,error:t}]of this.#ar)e(t);this.#ar.clear(),this.#sr.clear()}browserClient(){return this.#nr}getCdpClient(e){const t=this.#sr.get(e);if(!t)throw new Error("Unknown CDP session ID");return t}sendCommand(e,t,r){return new Promise(((n,s)=>{const a=this.#ir++;this.#ar.set(a,{resolve:n,reject:s,error:new Ht.CloseError(`${e} ${JSON.stringify(t)} ${r??""} call rejected because the connection has been closed.`)});const i={id:a,method:e,params:t};r&&(i.sessionId=r);const o=JSON.stringify(i);this.#Yt.sendMessage(o)?.catch((e=>{this.#t?.(`${Wt.LogType.cdp}:ERROR`,e),this.#Yt.close()})),this.#t?.(`${Wt.LogType.cdp}:SEND \u25b8`,JSON.stringify(i,null,2))}))}#or=e=>{const t=JSON.parse(e),r=JSON.stringify(t,null,2);if(this.#t?.(`${Wt.LogType.cdp}:RECV \u25c2`,r),"Target.attachedToTarget"===t.method){const{sessionId:e}=t.params;this.#sr.set(e,new Ht.CdpClient(this,e))}else if("Target.detachedFromTarget"===t.method){const{sessionId:e}=t.params;this.#sr.get(e)&&this.#sr.delete(e)}if(void 0!==t.id){const e=this.#ar.get(t.id);this.#ar.delete(t.id),e&&(t.result?e.resolve(t.result):t.error&&e.reject(t.error))}else if(t.method){const e=t.sessionId?this.#sr.get(t.sessionId):this.#nr;e?.emit(t.method,t.params||{})}}};var Jt={};Object.defineProperty(Jt,"__esModule",{value:!0}),Jt.log=Jt.generatePage=void 0;const Gt=P;function Xt(e){const t=`${e}_log`,r=document.getElementById(t);if(r)return r;const n=document.getElementById("details"),s=document.createElement("div");s.className="divider",n.appendChild(s);const a=document.createElement("div");return a.className="item",a.innerHTML=`<h3>${e}</h3><div id="${t}" class="log"></div>`,n.appendChild(a),document.getElementById(t)}Jt.generatePage=function(){globalThis.document.documentElement&&(globalThis.document.documentElement.innerHTML='<!DOCTYPE html><title>BiDi-CDP Mapper</title><style>body{font-family: Roboto, serif; font-size: 13px; color: #202124;}.log{padding: 12px; font-family: Menlo, Consolas, Monaco, Liberation Mono, Lucida Console, monospace; font-size: 11px; line-height: 180%; background: #f1f3f4; border-radius: 4px;}.pre{overflow-wrap: break-word; padding: 10px;}.card{margin: 60px auto; padding: 2px 0; max-width: 900px; box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15), 0 1px 6px rgba(0, 0, 0, 0.2); border-radius: 8px;}.divider{height: 1px; background: #f0f0f0;}.item{padding: 16px 20px;}</style><div class="card"><div class="item"><h1>BiDi-CDP Mapper is controlling this tab</h1><p>Closing or reloading it will stop the BiDi process. <a target="_blank" title="BiDi-CDP Mapper GitHub Repository" href="https://github.com/GoogleChromeLabs/chromium-bidi">Details.</a></p></div><div class="divider"></div><details id="details"><summary class="item">Debug information</summary></details></div>',Xt(Gt.LogType.system),Xt(Gt.LogType.bidi),Xt(Gt.LogType.browsingContexts),Xt(Gt.LogType.cdp))},Jt.log=function(t,...r){if(!globalThis.document.documentElement)return;e.window?.sendDebugMessage?.(JSON.stringify({logType:t,messages:r}));const n=Xt(t),s=document.createElement("div");s.className="pre",s.textContent=r.join(" "),n.appendChild(s)};
/**
	 * Copyright 2021 Google LLC.
	 * Copyright (c) Microsoft Corporation.
	 *
	 * Licensed under the Apache License, Version 2.0 (the "License");
	 * you may not use this file except in compliance with the License.
	 * You may obtain a copy of the License at
	 *
	 *     http://www.apache.org/licenses/LICENSE-2.0
	 *
	 * Unless required by applicable law or agreed to in writing, software
	 * distributed under the License is distributed on an "AS IS" BASIS,
	 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
	 * See the License for the specific language governing permissions and
	 * limitations under the License.
	 *
	 * @license
	 */
var Yt=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),Qt=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),er=e&&e.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&Yt(t,e,r);return Qt(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const tr=er(r),rr=b,nr=C,sr=Ft,ar=P,ir=Jt,or=async function(){return new Promise((e=>{window.setSelfTargetId=t=>{(0,ir.log)(ar.LogType.system,"Current target ID:",t),e(t)}}))}();(async()=>{(0,ir.generatePage)();const e=await or,t=await function(e){class t{#or=null;constructor(){window.onBidiMessage=e=>{let r;(0,ir.log)(`${ar.LogType.bidi}:RECV \u25c2`,e);try{r=t.#dr(e)}catch(t){return void this.#cr(e,rr.Message.ErrorCode.InvalidArgument,t.message,null)}this.#or?.call(null,r)}}setOnMessage(e){this.#or=e}sendMessage(e){const t=JSON.stringify(e);window.sendBidiResponse(t),(0,ir.log)(`${ar.LogType.bidi}:SEND \u25b8`,t)}close(){this.#or=null,window.onBidiMessage=null}#cr(e,r,n,s){const a=t.#ur(e,r,n);s?this.sendMessage({...a,channel:s}):this.sendMessage(a)}static#lr(e){return null===e?"null":Array.isArray(e)?"array":typeof e}static#ur(e,r,n){let s;try{const r=JSON.parse(e);"object"===t.#lr(r)&&"id"in r&&(s=r.id)}catch{}return{id:s,error:r,message:n}}static#dr(e){let r;try{r=JSON.parse(e)}catch{throw new Error("Cannot parse data as JSON")}const n=t.#lr(r);if("object"!==n)throw new Error(`Expected JSON object but got ${n}`);const{id:s,method:a,params:i}=r,o=t.#lr(s);if("number"!==o||!Number.isInteger(s)||s<0)throw new Error(`Expected unsigned integer but got ${o}`);const d=t.#lr(a);if("string"!==d)throw new Error(`Expected string method but got ${d}`);const c=t.#lr(i);if("object"!==c)throw new Error(`Expected object params but got ${c}`);let u=r.channel;if(void 0!==u){const e=t.#lr(u);if("string"!==e)throw new Error(`Expected string channel but got ${e}`);""===u&&(u=void 0)}return{id:s,method:a,params:i,channel:u}}}return nr.BidiServer.createAndStart(new t,function(){class e{#or=null;constructor(){window.cdp.onmessage=e=>{this.#or?.call(null,e)}}setOnMessage(e){this.#or=e}sendMessage(e){window.cdp.send(e)}close(){this.#or=null,window.cdp.onmessage=null}}return new sr.CdpConnection(new e,ir.log)}(),e,new dr,ir.log)}(e);(0,ir.log)(ar.LogType.system,"Launched"),t.emitOutgoingMessage(nr.OutgoingBidiMessage.createResolved({launched:!0},null))})();class dr{parseAddPreloadScriptParams(e){return tr.Script.parseAddPreloadScriptParams(e)}parseRemovePreloadScriptParams(e){return tr.Script.parseRemovePreloadScriptParams(e)}parseGetRealmsParams(e){return tr.Script.parseGetRealmsParams(e)}parseCallFunctionParams(e){return tr.Script.parseCallFunctionParams(e)}parseEvaluateParams(e){return tr.Script.parseEvaluateParams(e)}parseDisownParams(e){return tr.Script.parseDisownParams(e)}parseSendCommandParams(e){return tr.Cdp.parseSendCommandParams(e)}parseGetSessionParams(e){return tr.Cdp.parseGetSessionParams(e)}parseSubscribeParams(e){return tr.Session.parseSubscribeParams(e)}parseNavigateParams(e){return tr.BrowsingContext.parseNavigateParams(e)}parseReloadParams(e){return tr.BrowsingContext.parseReloadParams(e)}parseGetTreeParams(e){return tr.BrowsingContext.parseGetTreeParams(e)}parseCreateParams(e){return tr.BrowsingContext.parseCreateParams(e)}parseCloseParams(e){return tr.BrowsingContext.parseCloseParams(e)}parseCaptureScreenshotParams(e){return tr.BrowsingContext.parseCaptureScreenshotParams(e)}parsePrintParams(e){return tr.BrowsingContext.parsePrintParams(e)}parsePerformActionsParams(e){return tr.Input.parsePerformActionsParams(e)}parseReleaseActionsParams(e){return tr.Input.parseReleaseActionsParams(e)}parseSetViewportParams(e){return tr.BrowsingContext.parseSetViewportParams(e)}}return t}();
//# sourceMappingURL=mapperTab.js.map
