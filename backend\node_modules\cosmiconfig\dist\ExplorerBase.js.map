{"version": 3, "file": "ExplorerBase.js", "names": ["ExplorerBase", "constructor", "options", "cache", "loadCache", "Map", "searchCache", "config", "validateConfig", "clearLoadCache", "clear", "clearSearchCache", "clearCaches", "searchPlaces", "for<PERSON>ach", "place", "loader<PERSON>ey", "path", "extname", "loader", "loaders", "Error", "getExtensionDescription", "shouldSearchStopWithResult", "result", "isEmpty", "ignoreEmptySearchPlaces", "nextDirectoryToSearch", "currentDir", "currentResult", "nextDir", "nextDirUp", "stopDir", "loadPackageProp", "filepath", "content", "parsed<PERSON><PERSON><PERSON>", "loadJson", "packagePropValue", "getPropertyByPath", "packageProp", "getLoaderEntryForFile", "basename", "bind", "loadedContentToCosmiconfigResult", "loadedContent", "forceProp", "undefined", "usePackagePropInConfigFiles", "validate<PERSON>ile<PERSON><PERSON>", "dir", "dirname", "ext"], "sources": ["../src/ExplorerBase.ts"], "sourcesContent": ["import path from 'path';\nimport { getPropertyByPath } from './getPropertyByPath';\nimport { Loader } from './index';\nimport { loaders } from './loaders';\nimport {\n  Cache,\n  CosmiconfigResult,\n  ExplorerOptions,\n  ExplorerOptionsSync,\n  LoadedFileContent,\n} from './types';\n\nclass ExplorerBase<T extends ExplorerOptions | ExplorerOptionsSync> {\n  protected readonly loadCache?: Cache;\n  protected readonly searchCache?: Cache;\n  protected readonly config: T;\n\n  public constructor(options: T) {\n    if (options.cache) {\n      this.loadCache = new Map();\n      this.searchCache = new Map();\n    }\n\n    this.config = options;\n    this.validateConfig();\n  }\n\n  public clearLoadCache(): void {\n    if (this.loadCache) {\n      this.loadCache.clear();\n    }\n  }\n\n  public clearSearchCache(): void {\n    if (this.searchCache) {\n      this.searchCache.clear();\n    }\n  }\n\n  public clearCaches(): void {\n    this.clearLoadCache();\n    this.clearSearchCache();\n  }\n\n  private validateConfig(): void {\n    const config = this.config;\n\n    config.searchPlaces.forEach((place): void => {\n      const loaderKey = path.extname(place) || 'noExt';\n      const loader = config.loaders[loaderKey];\n      if (!loader) {\n        throw new Error(\n          `No loader specified for ${getExtensionDescription(\n            place,\n          )}, so searchPlaces item \"${place}\" is invalid`,\n        );\n      }\n\n      if (typeof loader !== 'function') {\n        throw new Error(\n          `loader for ${getExtensionDescription(\n            place,\n          )} is not a function (type provided: \"${typeof loader}\"), so searchPlaces item \"${place}\" is invalid`,\n        );\n      }\n    });\n  }\n\n  protected shouldSearchStopWithResult(result: CosmiconfigResult): boolean {\n    if (result === null) return false;\n    return !(result.isEmpty && this.config.ignoreEmptySearchPlaces);\n  }\n\n  protected nextDirectoryToSearch(\n    currentDir: string,\n    currentResult: CosmiconfigResult,\n  ): string | null {\n    if (this.shouldSearchStopWithResult(currentResult)) {\n      return null;\n    }\n    const nextDir = nextDirUp(currentDir);\n    if (nextDir === currentDir || currentDir === this.config.stopDir) {\n      return null;\n    }\n    return nextDir;\n  }\n\n  private loadPackageProp(filepath: string, content: string): unknown {\n    const parsedContent = loaders.loadJson(filepath, content);\n    const packagePropValue = getPropertyByPath(\n      parsedContent,\n      this.config.packageProp,\n    );\n    return packagePropValue || null;\n  }\n\n  protected getLoaderEntryForFile(filepath: string): Loader {\n    if (path.basename(filepath) === 'package.json') {\n      return this.loadPackageProp.bind(this);\n    }\n\n    const loaderKey = path.extname(filepath) || 'noExt';\n\n    const loader = this.config.loaders[loaderKey];\n\n    if (!loader) {\n      throw new Error(\n        `No loader specified for ${getExtensionDescription(filepath)}`,\n      );\n    }\n\n    return loader;\n  }\n\n  protected loadedContentToCosmiconfigResult(\n    filepath: string,\n    loadedContent: LoadedFileContent,\n    forceProp: boolean,\n  ): CosmiconfigResult {\n    if (loadedContent === null) {\n      return null;\n    }\n    if (loadedContent === undefined) {\n      return { filepath, config: undefined, isEmpty: true };\n    }\n    if (this.config.usePackagePropInConfigFiles || forceProp) {\n      loadedContent = getPropertyByPath(loadedContent, this.config.packageProp);\n    }\n    if (loadedContent === undefined) {\n      return { filepath, config: undefined, isEmpty: true };\n    }\n    return { config: loadedContent, filepath };\n  }\n\n  protected validateFilePath(filepath: string): void {\n    if (!filepath) {\n      throw new Error('load must pass a non-empty string');\n    }\n  }\n}\n\nfunction nextDirUp(dir: string): string {\n  return path.dirname(dir);\n}\n\nfunction getExtensionDescription(filepath: string): string {\n  const ext = path.extname(filepath);\n  return ext ? `extension \"${ext}\"` : 'files without extensions';\n}\n\nexport { ExplorerBase, getExtensionDescription };\n"], "mappings": ";;;;;;;;AAAA;;AACA;;AAEA;;;;AASA,MAAMA,YAAN,CAAoE;EAK3DC,WAAW,CAACC,OAAD,EAAa;IAC7B,IAAIA,OAAO,CAACC,KAAZ,EAAmB;MACjB,KAAKC,SAAL,GAAiB,IAAIC,GAAJ,EAAjB;MACA,KAAKC,WAAL,GAAmB,IAAID,GAAJ,EAAnB;IACD;;IAED,KAAKE,MAAL,GAAcL,OAAd;IACA,KAAKM,cAAL;EACD;;EAEMC,cAAc,GAAS;IAC5B,IAAI,KAAKL,SAAT,EAAoB;MAClB,KAAKA,SAAL,CAAeM,KAAf;IACD;EACF;;EAEMC,gBAAgB,GAAS;IAC9B,IAAI,KAAKL,WAAT,EAAsB;MACpB,KAAKA,WAAL,CAAiBI,KAAjB;IACD;EACF;;EAEME,WAAW,GAAS;IACzB,KAAKH,cAAL;IACA,KAAKE,gBAAL;EACD;;EAEOH,cAAc,GAAS;IAC7B,MAAMD,MAAM,GAAG,KAAKA,MAApB;IAEAA,MAAM,CAACM,YAAP,CAAoBC,OAApB,CAA6BC,KAAD,IAAiB;MAC3C,MAAMC,SAAS,GAAGC,aAAA,CAAKC,OAAL,CAAaH,KAAb,KAAuB,OAAzC;MACA,MAAMI,MAAM,GAAGZ,MAAM,CAACa,OAAP,CAAeJ,SAAf,CAAf;;MACA,IAAI,CAACG,MAAL,EAAa;QACX,MAAM,IAAIE,KAAJ,CACH,2BAA0BC,uBAAuB,CAChDP,KADgD,CAEhD,2BAA0BA,KAAM,cAH9B,CAAN;MAKD;;MAED,IAAI,OAAOI,MAAP,KAAkB,UAAtB,EAAkC;QAChC,MAAM,IAAIE,KAAJ,CACH,cAAaC,uBAAuB,CACnCP,KADmC,CAEnC,uCAAsC,OAAOI,MAAO,6BAA4BJ,KAAM,cAHpF,CAAN;MAKD;IACF,CAlBD;EAmBD;;EAESQ,0BAA0B,CAACC,MAAD,EAAqC;IACvE,IAAIA,MAAM,KAAK,IAAf,EAAqB,OAAO,KAAP;IACrB,OAAO,EAAEA,MAAM,CAACC,OAAP,IAAkB,KAAKlB,MAAL,CAAYmB,uBAAhC,CAAP;EACD;;EAESC,qBAAqB,CAC7BC,UAD6B,EAE7BC,aAF6B,EAGd;IACf,IAAI,KAAKN,0BAAL,CAAgCM,aAAhC,CAAJ,EAAoD;MAClD,OAAO,IAAP;IACD;;IACD,MAAMC,OAAO,GAAGC,SAAS,CAACH,UAAD,CAAzB;;IACA,IAAIE,OAAO,KAAKF,UAAZ,IAA0BA,UAAU,KAAK,KAAKrB,MAAL,CAAYyB,OAAzD,EAAkE;MAChE,OAAO,IAAP;IACD;;IACD,OAAOF,OAAP;EACD;;EAEOG,eAAe,CAACC,QAAD,EAAmBC,OAAnB,EAA6C;IAClE,MAAMC,aAAa,GAAGhB,gBAAA,CAAQiB,QAAR,CAAiBH,QAAjB,EAA2BC,OAA3B,CAAtB;;IACA,MAAMG,gBAAgB,GAAG,IAAAC,oCAAA,EACvBH,aADuB,EAEvB,KAAK7B,MAAL,CAAYiC,WAFW,CAAzB;IAIA,OAAOF,gBAAgB,IAAI,IAA3B;EACD;;EAESG,qBAAqB,CAACP,QAAD,EAA2B;IACxD,IAAIjB,aAAA,CAAKyB,QAAL,CAAcR,QAAd,MAA4B,cAAhC,EAAgD;MAC9C,OAAO,KAAKD,eAAL,CAAqBU,IAArB,CAA0B,IAA1B,CAAP;IACD;;IAED,MAAM3B,SAAS,GAAGC,aAAA,CAAKC,OAAL,CAAagB,QAAb,KAA0B,OAA5C;IAEA,MAAMf,MAAM,GAAG,KAAKZ,MAAL,CAAYa,OAAZ,CAAoBJ,SAApB,CAAf;;IAEA,IAAI,CAACG,MAAL,EAAa;MACX,MAAM,IAAIE,KAAJ,CACH,2BAA0BC,uBAAuB,CAACY,QAAD,CAAW,EADzD,CAAN;IAGD;;IAED,OAAOf,MAAP;EACD;;EAESyB,gCAAgC,CACxCV,QADwC,EAExCW,aAFwC,EAGxCC,SAHwC,EAIrB;IACnB,IAAID,aAAa,KAAK,IAAtB,EAA4B;MAC1B,OAAO,IAAP;IACD;;IACD,IAAIA,aAAa,KAAKE,SAAtB,EAAiC;MAC/B,OAAO;QAAEb,QAAF;QAAY3B,MAAM,EAAEwC,SAApB;QAA+BtB,OAAO,EAAE;MAAxC,CAAP;IACD;;IACD,IAAI,KAAKlB,MAAL,CAAYyC,2BAAZ,IAA2CF,SAA/C,EAA0D;MACxDD,aAAa,GAAG,IAAAN,oCAAA,EAAkBM,aAAlB,EAAiC,KAAKtC,MAAL,CAAYiC,WAA7C,CAAhB;IACD;;IACD,IAAIK,aAAa,KAAKE,SAAtB,EAAiC;MAC/B,OAAO;QAAEb,QAAF;QAAY3B,MAAM,EAAEwC,SAApB;QAA+BtB,OAAO,EAAE;MAAxC,CAAP;IACD;;IACD,OAAO;MAAElB,MAAM,EAAEsC,aAAV;MAAyBX;IAAzB,CAAP;EACD;;EAESe,gBAAgB,CAACf,QAAD,EAAyB;IACjD,IAAI,CAACA,QAAL,EAAe;MACb,MAAM,IAAIb,KAAJ,CAAU,mCAAV,CAAN;IACD;EACF;;AA9HiE;;;;AAiIpE,SAASU,SAAT,CAAmBmB,GAAnB,EAAwC;EACtC,OAAOjC,aAAA,CAAKkC,OAAL,CAAaD,GAAb,CAAP;AACD;;AAED,SAAS5B,uBAAT,CAAiCY,QAAjC,EAA2D;EACzD,MAAMkB,GAAG,GAAGnC,aAAA,CAAKC,OAAL,CAAagB,QAAb,CAAZ;;EACA,OAAOkB,GAAG,GAAI,cAAaA,GAAI,GAArB,GAA0B,0BAApC;AACD"}