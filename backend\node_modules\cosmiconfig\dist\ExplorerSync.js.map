{"version": 3, "file": "ExplorerSync.js", "names": ["ExplorerSync", "ExplorerBase", "constructor", "options", "searchSync", "searchFrom", "process", "cwd", "config", "metaConfigFilePath", "_loadFileSync", "isEmpty", "searchFromDirectorySync", "getDirectorySync", "dir", "absoluteDir", "path", "resolve", "run", "result", "searchDirectorySync", "nextDir", "nextDirectoryToSearch", "transform", "searchCache", "cacheWrapperSync", "place", "searchPlaces", "placeResult", "loadSearchPlaceSync", "shouldSearchStopWithResult", "filepath", "join", "content", "readFileSync", "createCosmiconfigResultSync", "loadFileContentSync", "trim", "undefined", "loader", "getLoaderEntryForFile", "e", "forceProp", "fileContent", "loadedContentToCosmiconfigResult", "loadSync", "validate<PERSON>ile<PERSON><PERSON>", "absoluteFilePath", "runLoadSync", "throwNotFound", "cosmiconfigResult", "loadCache"], "sources": ["../src/ExplorerSync.ts"], "sourcesContent": ["import path from 'path';\nimport { cacheWrapperSync } from './cacheWrapper';\nimport { ExplorerBase } from './ExplorerBase';\nimport { getDirectorySync } from './getDirectory';\nimport { readFileSync } from './readFile';\nimport {\n  CosmiconfigResult,\n  ExplorerOptionsSync,\n  LoadedFileContent,\n} from './types';\n\nclass ExplorerSync extends ExplorerBase<ExplorerOptionsSync> {\n  public constructor(options: ExplorerOptionsSync) {\n    super(options);\n  }\n\n  public searchSync(searchFrom: string = process.cwd()): CosmiconfigResult {\n    if (this.config.metaConfigFilePath) {\n      const config = this._loadFileSync(this.config.metaConfigFilePath, true);\n      if (config && !config.isEmpty) {\n        return config;\n      }\n    }\n    return this.searchFromDirectorySync(getDirectorySync(searchFrom));\n  }\n\n  private searchFromDirectorySync(dir: string): CosmiconfigResult {\n    const absoluteDir = path.resolve(process.cwd(), dir);\n\n    const run = (): CosmiconfigResult => {\n      const result = this.searchDirectorySync(absoluteDir);\n      const nextDir = this.nextDirectoryToSearch(absoluteDir, result);\n\n      if (nextDir) {\n        return this.searchFromDirectorySync(nextDir);\n      }\n\n      return this.config.transform(result);\n    };\n\n    if (this.searchCache) {\n      return cacheWrapperSync(this.searchCache, absoluteDir, run);\n    }\n\n    return run();\n  }\n\n  private searchDirectorySync(dir: string): CosmiconfigResult {\n    for (const place of this.config.searchPlaces) {\n      const placeResult = this.loadSearchPlaceSync(dir, place);\n\n      if (this.shouldSearchStopWithResult(placeResult)) {\n        return placeResult;\n      }\n    }\n\n    // config not found\n    return null;\n  }\n\n  private loadSearchPlaceSync(dir: string, place: string): CosmiconfigResult {\n    const filepath = path.join(dir, place);\n    const content = readFileSync(filepath);\n\n    return this.createCosmiconfigResultSync(filepath, content, false);\n  }\n\n  private loadFileContentSync(\n    filepath: string,\n    content: string | null,\n  ): LoadedFileContent {\n    if (content === null) {\n      return null;\n    }\n    if (content.trim() === '') {\n      return undefined;\n    }\n    const loader = this.getLoaderEntryForFile(filepath);\n    try {\n      return loader(filepath, content);\n    } catch (e: any) {\n      e.filepath = filepath;\n      throw e;\n    }\n  }\n\n  private createCosmiconfigResultSync(\n    filepath: string,\n    content: string | null,\n    forceProp: boolean,\n  ): CosmiconfigResult {\n    const fileContent = this.loadFileContentSync(filepath, content);\n\n    return this.loadedContentToCosmiconfigResult(\n      filepath,\n      fileContent,\n      forceProp,\n    );\n  }\n\n  public loadSync(filepath: string): CosmiconfigResult {\n    return this._loadFileSync(filepath, false);\n  }\n\n  private _loadFileSync(\n    filepath: string,\n    forceProp: boolean,\n  ): CosmiconfigResult {\n    this.validateFilePath(filepath);\n    const absoluteFilePath = path.resolve(process.cwd(), filepath);\n\n    const runLoadSync = (): CosmiconfigResult => {\n      const content = readFileSync(absoluteFilePath, { throwNotFound: true });\n      const cosmiconfigResult = this.createCosmiconfigResultSync(\n        absoluteFilePath,\n        content,\n        forceProp,\n      );\n\n      return this.config.transform(cosmiconfigResult);\n    };\n\n    if (this.loadCache) {\n      return cacheWrapperSync(this.loadCache, absoluteFilePath, runLoadSync);\n    }\n\n    return runLoadSync();\n  }\n}\n\nexport { ExplorerSync };\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;;;AAOA,MAAMA,YAAN,SAA2BC,0BAA3B,CAA6D;EACpDC,WAAW,CAACC,OAAD,EAA+B;IAC/C,MAAMA,OAAN;EACD;;EAEMC,UAAU,CAACC,UAAkB,GAAGC,OAAO,CAACC,GAAR,EAAtB,EAAwD;IACvE,IAAI,KAAKC,MAAL,CAAYC,kBAAhB,EAAoC;MAClC,MAAMD,MAAM,GAAG,KAAKE,aAAL,CAAmB,KAAKF,MAAL,CAAYC,kBAA/B,EAAmD,IAAnD,CAAf;;MACA,IAAID,MAAM,IAAI,CAACA,MAAM,CAACG,OAAtB,EAA+B;QAC7B,OAAOH,MAAP;MACD;IACF;;IACD,OAAO,KAAKI,uBAAL,CAA6B,IAAAC,8BAAA,EAAiBR,UAAjB,CAA7B,CAAP;EACD;;EAEOO,uBAAuB,CAACE,GAAD,EAAiC;IAC9D,MAAMC,WAAW,GAAGC,aAAA,CAAKC,OAAL,CAAaX,OAAO,CAACC,GAAR,EAAb,EAA4BO,GAA5B,CAApB;;IAEA,MAAMI,GAAG,GAAG,MAAyB;MACnC,MAAMC,MAAM,GAAG,KAAKC,mBAAL,CAAyBL,WAAzB,CAAf;MACA,MAAMM,OAAO,GAAG,KAAKC,qBAAL,CAA2BP,WAA3B,EAAwCI,MAAxC,CAAhB;;MAEA,IAAIE,OAAJ,EAAa;QACX,OAAO,KAAKT,uBAAL,CAA6BS,OAA7B,CAAP;MACD;;MAED,OAAO,KAAKb,MAAL,CAAYe,SAAZ,CAAsBJ,MAAtB,CAAP;IACD,CATD;;IAWA,IAAI,KAAKK,WAAT,EAAsB;MACpB,OAAO,IAAAC,8BAAA,EAAiB,KAAKD,WAAtB,EAAmCT,WAAnC,EAAgDG,GAAhD,CAAP;IACD;;IAED,OAAOA,GAAG,EAAV;EACD;;EAEOE,mBAAmB,CAACN,GAAD,EAAiC;IAC1D,KAAK,MAAMY,KAAX,IAAoB,KAAKlB,MAAL,CAAYmB,YAAhC,EAA8C;MAC5C,MAAMC,WAAW,GAAG,KAAKC,mBAAL,CAAyBf,GAAzB,EAA8BY,KAA9B,CAApB;;MAEA,IAAI,KAAKI,0BAAL,CAAgCF,WAAhC,CAAJ,EAAkD;QAChD,OAAOA,WAAP;MACD;IACF,CAPyD,CAS1D;;;IACA,OAAO,IAAP;EACD;;EAEOC,mBAAmB,CAACf,GAAD,EAAcY,KAAd,EAAgD;IACzE,MAAMK,QAAQ,GAAGf,aAAA,CAAKgB,IAAL,CAAUlB,GAAV,EAAeY,KAAf,CAAjB;;IACA,MAAMO,OAAO,GAAG,IAAAC,sBAAA,EAAaH,QAAb,CAAhB;IAEA,OAAO,KAAKI,2BAAL,CAAiCJ,QAAjC,EAA2CE,OAA3C,EAAoD,KAApD,CAAP;EACD;;EAEOG,mBAAmB,CACzBL,QADyB,EAEzBE,OAFyB,EAGN;IACnB,IAAIA,OAAO,KAAK,IAAhB,EAAsB;MACpB,OAAO,IAAP;IACD;;IACD,IAAIA,OAAO,CAACI,IAAR,OAAmB,EAAvB,EAA2B;MACzB,OAAOC,SAAP;IACD;;IACD,MAAMC,MAAM,GAAG,KAAKC,qBAAL,CAA2BT,QAA3B,CAAf;;IACA,IAAI;MACF,OAAOQ,MAAM,CAACR,QAAD,EAAWE,OAAX,CAAb;IACD,CAFD,CAEE,OAAOQ,CAAP,EAAe;MACfA,CAAC,CAACV,QAAF,GAAaA,QAAb;MACA,MAAMU,CAAN;IACD;EACF;;EAEON,2BAA2B,CACjCJ,QADiC,EAEjCE,OAFiC,EAGjCS,SAHiC,EAId;IACnB,MAAMC,WAAW,GAAG,KAAKP,mBAAL,CAAyBL,QAAzB,EAAmCE,OAAnC,CAApB;IAEA,OAAO,KAAKW,gCAAL,CACLb,QADK,EAELY,WAFK,EAGLD,SAHK,CAAP;EAKD;;EAEMG,QAAQ,CAACd,QAAD,EAAsC;IACnD,OAAO,KAAKrB,aAAL,CAAmBqB,QAAnB,EAA6B,KAA7B,CAAP;EACD;;EAEOrB,aAAa,CACnBqB,QADmB,EAEnBW,SAFmB,EAGA;IACnB,KAAKI,gBAAL,CAAsBf,QAAtB;;IACA,MAAMgB,gBAAgB,GAAG/B,aAAA,CAAKC,OAAL,CAAaX,OAAO,CAACC,GAAR,EAAb,EAA4BwB,QAA5B,CAAzB;;IAEA,MAAMiB,WAAW,GAAG,MAAyB;MAC3C,MAAMf,OAAO,GAAG,IAAAC,sBAAA,EAAaa,gBAAb,EAA+B;QAAEE,aAAa,EAAE;MAAjB,CAA/B,CAAhB;MACA,MAAMC,iBAAiB,GAAG,KAAKf,2BAAL,CACxBY,gBADwB,EAExBd,OAFwB,EAGxBS,SAHwB,CAA1B;MAMA,OAAO,KAAKlC,MAAL,CAAYe,SAAZ,CAAsB2B,iBAAtB,CAAP;IACD,CATD;;IAWA,IAAI,KAAKC,SAAT,EAAoB;MAClB,OAAO,IAAA1B,8BAAA,EAAiB,KAAK0B,SAAtB,EAAiCJ,gBAAjC,EAAmDC,WAAnD,CAAP;IACD;;IAED,OAAOA,WAAW,EAAlB;EACD;;AApH0D"}