{"version": 3, "file": "index.js", "names": ["metaSearchPlaces", "defaultLoaders", "Object", "freeze", "loaders", "loadJs", "loadJson", "loadYaml", "noExt", "defaultLoadersSync", "loadJsSync", "identity", "x", "replaceMetaPlaceholders", "paths", "moduleName", "map", "path", "replace", "getExplorerOptions", "options", "metaExplorer", "ExplorerSync", "packageProp", "stopDir", "process", "cwd", "searchPlaces", "ignoreEmptySearchPlaces", "usePackagePropInConfigFiles", "transform", "cache", "metaConfigFilePath", "metaConfig", "searchSync", "config", "Error", "overrideOptions", "filepath", "cosmiconfig", "explorerOptions", "normalizedOptions", "normalizeOptions", "explorer", "Explorer", "search", "bind", "load", "clearLoadCache", "clearSearchCache", "clearCaches", "cosmiconfigSync", "normalizeOptionsSync", "explorerSync", "loadSync", "defaults", "filter", "Boolean", "os", "homedir"], "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport os from 'os';\nimport { Explorer } from './Explorer';\nimport { ExplorerSync } from './ExplorerSync';\nimport { loaders } from './loaders';\nimport {\n  Config,\n  CosmiconfigResult,\n  ExplorerOptions,\n  ExplorerOptionsSync,\n  Loaders,\n  LoadersSync,\n} from './types';\n\ntype LoaderResult = Config | null;\nexport type Loader =\n  | ((filepath: string, content: string) => Promise<LoaderResult>)\n  | LoaderSync;\nexport type LoaderSync = (filepath: string, content: string) => LoaderResult;\n\nexport type Transform =\n  | ((CosmiconfigResult: CosmiconfigResult) => Promise<CosmiconfigResult>)\n  | TransformSync;\n\nexport type TransformSync = (\n  CosmiconfigResult: CosmiconfigResult,\n) => CosmiconfigResult;\n\ninterface OptionsBase {\n  packageProp?: string | Array<string>;\n  searchPlaces?: Array<string>;\n  ignoreEmptySearchPlaces?: boolean;\n  stopDir?: string;\n  cache?: boolean;\n}\n\nexport interface Options extends OptionsBase {\n  loaders?: Loaders;\n  transform?: Transform;\n}\n\nexport interface OptionsSync extends OptionsBase {\n  loaders?: LoadersSync;\n  transform?: TransformSync;\n}\n\nexport interface PublicExplorerBase {\n  clearLoadCache: () => void;\n  clearSearchCache: () => void;\n  clearCaches: () => void;\n}\n\nexport interface PublicExplorer extends PublicExplorerBase {\n  search: (searchFrom?: string) => Promise<CosmiconfigResult>;\n  load: (filepath: string) => Promise<CosmiconfigResult>;\n}\n\nexport interface PublicExplorerSync extends PublicExplorerBase {\n  search: (searchFrom?: string) => CosmiconfigResult;\n  load: (filepath: string) => CosmiconfigResult;\n}\n\n// this needs to be hardcoded, as this is intended for end users, who can't supply options at this point\nexport const metaSearchPlaces = [\n  'package.json',\n  '.config.json',\n  '.config.yaml',\n  '.config.yml',\n  '.config.js',\n  '.config.cjs',\n  '.config.mjs',\n];\n\n// do not allow mutation of default loaders. Make sure it is set inside options\nconst defaultLoaders = Object.freeze({\n  '.mjs': loaders.loadJs,\n  '.cjs': loaders.loadJs,\n  '.js': loaders.loadJs,\n  '.json': loaders.loadJson,\n  '.yaml': loaders.loadYaml,\n  '.yml': loaders.loadYaml,\n  noExt: loaders.loadYaml,\n} as const);\nconst defaultLoadersSync = Object.freeze({\n  '.cjs': loaders.loadJsSync,\n  '.js': loaders.loadJsSync,\n  '.json': loaders.loadJson,\n  '.yaml': loaders.loadYaml,\n  '.yml': loaders.loadYaml,\n  noExt: loaders.loadYaml,\n} as const);\n\nconst identity: TransformSync = function identity(x) {\n  return x;\n};\n\nfunction replaceMetaPlaceholders(\n  paths: Array<string>,\n  moduleName: string,\n): Array<string> {\n  return paths.map((path) => path.replace('{name}', moduleName));\n}\n\nfunction getExplorerOptions<T extends Options | OptionsSync>(\n  moduleName: string,\n  options: T,\n): T {\n  const metaExplorer = new ExplorerSync({\n    packageProp: 'cosmiconfig',\n    stopDir: process.cwd(),\n    searchPlaces: metaSearchPlaces,\n    ignoreEmptySearchPlaces: false,\n    usePackagePropInConfigFiles: true,\n    loaders: defaultLoaders,\n    transform: identity,\n    cache: true,\n    metaConfigFilePath: null,\n  });\n  const metaConfig = metaExplorer.searchSync();\n\n  if (!metaConfig) {\n    return options;\n  }\n\n  if (metaConfig.config?.loaders) {\n    throw new Error('Can not specify loaders in meta config file');\n  }\n\n  const overrideOptions = metaConfig.config ?? {};\n\n  if (overrideOptions.searchPlaces) {\n    overrideOptions.searchPlaces = replaceMetaPlaceholders(\n      overrideOptions.searchPlaces,\n      moduleName,\n    );\n  }\n\n  overrideOptions.metaConfigFilePath = metaConfig.filepath;\n\n  return { ...options, ...overrideOptions };\n}\n\nfunction cosmiconfig(\n  moduleName: string,\n  options: Options = {},\n): PublicExplorer {\n  const explorerOptions = getExplorerOptions(moduleName, options);\n\n  const normalizedOptions: ExplorerOptions = normalizeOptions(\n    moduleName,\n    explorerOptions,\n  );\n\n  const explorer = new Explorer(normalizedOptions);\n\n  return {\n    search: explorer.search.bind(explorer),\n    load: explorer.load.bind(explorer),\n    clearLoadCache: explorer.clearLoadCache.bind(explorer),\n    clearSearchCache: explorer.clearSearchCache.bind(explorer),\n    clearCaches: explorer.clearCaches.bind(explorer),\n  };\n}\n\n// eslint-disable-next-line @typescript-eslint/explicit-function-return-type\nfunction cosmiconfigSync(\n  moduleName: string,\n  options: OptionsSync = {},\n): PublicExplorerSync {\n  const explorerOptions = getExplorerOptions(moduleName, options);\n\n  const normalizedOptions: ExplorerOptionsSync = normalizeOptionsSync(\n    moduleName,\n    explorerOptions,\n  );\n\n  const explorerSync = new ExplorerSync(normalizedOptions);\n\n  return {\n    search: explorerSync.searchSync.bind(explorerSync),\n    load: explorerSync.loadSync.bind(explorerSync),\n    clearLoadCache: explorerSync.clearLoadCache.bind(explorerSync),\n    clearSearchCache: explorerSync.clearSearchCache.bind(explorerSync),\n    clearCaches: explorerSync.clearCaches.bind(explorerSync),\n  };\n}\n\nfunction normalizeOptions(\n  moduleName: string,\n  options: Options,\n): ExplorerOptions {\n  const defaults: ExplorerOptions = {\n    packageProp: moduleName,\n    searchPlaces: [\n      'package.json',\n      `.${moduleName}rc`,\n      `.${moduleName}rc.json`,\n      `.${moduleName}rc.yaml`,\n      `.${moduleName}rc.yml`,\n      `.${moduleName}rc.js`,\n      `.${moduleName}rc.cjs`,\n      `.${moduleName}rc.mjs`,\n      `.config/${moduleName}rc`,\n      `.config/${moduleName}rc.json`,\n      `.config/${moduleName}rc.yaml`,\n      `.config/${moduleName}rc.yml`,\n      `.config/${moduleName}rc.js`,\n      `.config/${moduleName}rc.cjs`,\n      `.config/${moduleName}rc.mjs`,\n      `${moduleName}.config.js`,\n      `${moduleName}.config.cjs`,\n      `${moduleName}.config.mjs`,\n    ].filter(Boolean),\n    ignoreEmptySearchPlaces: true,\n    stopDir: os.homedir(),\n    cache: true,\n    transform: identity,\n    loaders: defaultLoaders,\n    metaConfigFilePath: null,\n  };\n\n  const normalizedOptions: ExplorerOptions = {\n    ...defaults,\n    ...options,\n    loaders: {\n      ...defaults.loaders,\n      ...options.loaders,\n    },\n  };\n\n  return normalizedOptions;\n}\n\nfunction normalizeOptionsSync(\n  moduleName: string,\n  options: OptionsSync,\n): ExplorerOptionsSync {\n  const defaults: ExplorerOptionsSync = {\n    packageProp: moduleName,\n    searchPlaces: [\n      'package.json',\n      `.${moduleName}rc`,\n      `.${moduleName}rc.json`,\n      `.${moduleName}rc.yaml`,\n      `.${moduleName}rc.yml`,\n      `.${moduleName}rc.js`,\n      `.${moduleName}rc.cjs`,\n      `.config/${moduleName}rc`,\n      `.config/${moduleName}rc.json`,\n      `.config/${moduleName}rc.yaml`,\n      `.config/${moduleName}rc.yml`,\n      `.config/${moduleName}rc.js`,\n      `.config/${moduleName}rc.cjs`,\n      `${moduleName}.config.js`,\n      `${moduleName}.config.cjs`,\n    ],\n    ignoreEmptySearchPlaces: true,\n    stopDir: os.homedir(),\n    cache: true,\n    transform: identity,\n    loaders: defaultLoadersSync,\n    metaConfigFilePath: null,\n  };\n\n  const normalizedOptions: ExplorerOptionsSync = {\n    ...defaults,\n    ...options,\n    loaders: {\n      ...defaults.loaders,\n      ...options.loaders,\n    },\n  };\n\n  return normalizedOptions;\n}\n\nexport { cosmiconfig, cosmiconfigSync, defaultLoaders, defaultLoadersSync };\n"], "mappings": ";;;;;;;;;AACA;;AACA;;AACA;;AACA;;AACA;;;;AALA;AA8DA;AACO,MAAMA,gBAAgB,GAAG,CAC9B,cAD8B,EAE9B,cAF8B,EAG9B,cAH8B,EAI9B,aAJ8B,EAK9B,YAL8B,EAM9B,aAN8B,EAO9B,aAP8B,CAAzB,C,CAUP;;;AACA,MAAMC,cAAc,GAAGC,MAAM,CAACC,MAAP,CAAc;EACnC,QAAQC,gBAAA,CAAQC,MADmB;EAEnC,QAAQD,gBAAA,CAAQC,MAFmB;EAGnC,OAAOD,gBAAA,CAAQC,MAHoB;EAInC,SAASD,gBAAA,CAAQE,QAJkB;EAKnC,SAASF,gBAAA,CAAQG,QALkB;EAMnC,QAAQH,gBAAA,CAAQG,QANmB;EAOnCC,KAAK,EAAEJ,gBAAA,CAAQG;AAPoB,CAAd,CAAvB;;AASA,MAAME,kBAAkB,GAAGP,MAAM,CAACC,MAAP,CAAc;EACvC,QAAQC,gBAAA,CAAQM,UADuB;EAEvC,OAAON,gBAAA,CAAQM,UAFwB;EAGvC,SAASN,gBAAA,CAAQE,QAHsB;EAIvC,SAASF,gBAAA,CAAQG,QAJsB;EAKvC,QAAQH,gBAAA,CAAQG,QALuB;EAMvCC,KAAK,EAAEJ,gBAAA,CAAQG;AANwB,CAAd,CAA3B;;;AASA,MAAMI,QAAuB,GAAG,SAASA,QAAT,CAAkBC,CAAlB,EAAqB;EACnD,OAAOA,CAAP;AACD,CAFD;;AAIA,SAASC,uBAAT,CACEC,KADF,EAEEC,UAFF,EAGiB;EACf,OAAOD,KAAK,CAACE,GAAN,CAAWC,IAAD,IAAUA,IAAI,CAACC,OAAL,CAAa,QAAb,EAAuBH,UAAvB,CAApB,CAAP;AACD;;AAED,SAASI,kBAAT,CACEJ,UADF,EAEEK,OAFF,EAGK;EAAA;;EACH,MAAMC,YAAY,GAAG,IAAIC,0BAAJ,CAAiB;IACpCC,WAAW,EAAE,aADuB;IAEpCC,OAAO,EAAEC,OAAO,CAACC,GAAR,EAF2B;IAGpCC,YAAY,EAAE3B,gBAHsB;IAIpC4B,uBAAuB,EAAE,KAJW;IAKpCC,2BAA2B,EAAE,IALO;IAMpCzB,OAAO,EAAEH,cAN2B;IAOpC6B,SAAS,EAAEnB,QAPyB;IAQpCoB,KAAK,EAAE,IAR6B;IASpCC,kBAAkB,EAAE;EATgB,CAAjB,CAArB;EAWA,MAAMC,UAAU,GAAGZ,YAAY,CAACa,UAAb,EAAnB;;EAEA,IAAI,CAACD,UAAL,EAAiB;IACf,OAAOb,OAAP;EACD;;EAED,0BAAIa,UAAU,CAACE,MAAf,+CAAI,mBAAmB/B,OAAvB,EAAgC;IAC9B,MAAM,IAAIgC,KAAJ,CAAU,6CAAV,CAAN;EACD;;EAED,MAAMC,eAAe,GAAGJ,UAAU,CAACE,MAAX,IAAqB,EAA7C;;EAEA,IAAIE,eAAe,CAACV,YAApB,EAAkC;IAChCU,eAAe,CAACV,YAAhB,GAA+Bd,uBAAuB,CACpDwB,eAAe,CAACV,YADoC,EAEpDZ,UAFoD,CAAtD;EAID;;EAEDsB,eAAe,CAACL,kBAAhB,GAAqCC,UAAU,CAACK,QAAhD;EAEA,OAAO,EAAE,GAAGlB,OAAL;IAAc,GAAGiB;EAAjB,CAAP;AACD;;AAED,SAASE,WAAT,CACExB,UADF,EAEEK,OAAgB,GAAG,EAFrB,EAGkB;EAChB,MAAMoB,eAAe,GAAGrB,kBAAkB,CAACJ,UAAD,EAAaK,OAAb,CAA1C;EAEA,MAAMqB,iBAAkC,GAAGC,gBAAgB,CACzD3B,UADyD,EAEzDyB,eAFyD,CAA3D;EAKA,MAAMG,QAAQ,GAAG,IAAIC,kBAAJ,CAAaH,iBAAb,CAAjB;EAEA,OAAO;IACLI,MAAM,EAAEF,QAAQ,CAACE,MAAT,CAAgBC,IAAhB,CAAqBH,QAArB,CADH;IAELI,IAAI,EAAEJ,QAAQ,CAACI,IAAT,CAAcD,IAAd,CAAmBH,QAAnB,CAFD;IAGLK,cAAc,EAAEL,QAAQ,CAACK,cAAT,CAAwBF,IAAxB,CAA6BH,QAA7B,CAHX;IAILM,gBAAgB,EAAEN,QAAQ,CAACM,gBAAT,CAA0BH,IAA1B,CAA+BH,QAA/B,CAJb;IAKLO,WAAW,EAAEP,QAAQ,CAACO,WAAT,CAAqBJ,IAArB,CAA0BH,QAA1B;EALR,CAAP;AAOD,C,CAED;;;AACA,SAASQ,eAAT,CACEpC,UADF,EAEEK,OAAoB,GAAG,EAFzB,EAGsB;EACpB,MAAMoB,eAAe,GAAGrB,kBAAkB,CAACJ,UAAD,EAAaK,OAAb,CAA1C;EAEA,MAAMqB,iBAAsC,GAAGW,oBAAoB,CACjErC,UADiE,EAEjEyB,eAFiE,CAAnE;EAKA,MAAMa,YAAY,GAAG,IAAI/B,0BAAJ,CAAiBmB,iBAAjB,CAArB;EAEA,OAAO;IACLI,MAAM,EAAEQ,YAAY,CAACnB,UAAb,CAAwBY,IAAxB,CAA6BO,YAA7B,CADH;IAELN,IAAI,EAAEM,YAAY,CAACC,QAAb,CAAsBR,IAAtB,CAA2BO,YAA3B,CAFD;IAGLL,cAAc,EAAEK,YAAY,CAACL,cAAb,CAA4BF,IAA5B,CAAiCO,YAAjC,CAHX;IAILJ,gBAAgB,EAAEI,YAAY,CAACJ,gBAAb,CAA8BH,IAA9B,CAAmCO,YAAnC,CAJb;IAKLH,WAAW,EAAEG,YAAY,CAACH,WAAb,CAAyBJ,IAAzB,CAA8BO,YAA9B;EALR,CAAP;AAOD;;AAED,SAASX,gBAAT,CACE3B,UADF,EAEEK,OAFF,EAGmB;EACjB,MAAMmC,QAAyB,GAAG;IAChChC,WAAW,EAAER,UADmB;IAEhCY,YAAY,EAAE,CACZ,cADY,EAEX,IAAGZ,UAAW,IAFH,EAGX,IAAGA,UAAW,SAHH,EAIX,IAAGA,UAAW,SAJH,EAKX,IAAGA,UAAW,QALH,EAMX,IAAGA,UAAW,OANH,EAOX,IAAGA,UAAW,QAPH,EAQX,IAAGA,UAAW,QARH,EASX,WAAUA,UAAW,IATV,EAUX,WAAUA,UAAW,SAVV,EAWX,WAAUA,UAAW,SAXV,EAYX,WAAUA,UAAW,QAZV,EAaX,WAAUA,UAAW,OAbV,EAcX,WAAUA,UAAW,QAdV,EAeX,WAAUA,UAAW,QAfV,EAgBX,GAAEA,UAAW,YAhBF,EAiBX,GAAEA,UAAW,aAjBF,EAkBX,GAAEA,UAAW,aAlBF,EAmBZyC,MAnBY,CAmBLC,OAnBK,CAFkB;IAsBhC7B,uBAAuB,EAAE,IAtBO;IAuBhCJ,OAAO,EAAEkC,WAAA,CAAGC,OAAH,EAvBuB;IAwBhC5B,KAAK,EAAE,IAxByB;IAyBhCD,SAAS,EAAEnB,QAzBqB;IA0BhCP,OAAO,EAAEH,cA1BuB;IA2BhC+B,kBAAkB,EAAE;EA3BY,CAAlC;EA8BA,MAAMS,iBAAkC,GAAG,EACzC,GAAGc,QADsC;IAEzC,GAAGnC,OAFsC;IAGzChB,OAAO,EAAE,EACP,GAAGmD,QAAQ,CAACnD,OADL;MAEP,GAAGgB,OAAO,CAAChB;IAFJ;EAHgC,CAA3C;EASA,OAAOqC,iBAAP;AACD;;AAED,SAASW,oBAAT,CACErC,UADF,EAEEK,OAFF,EAGuB;EACrB,MAAMmC,QAA6B,GAAG;IACpChC,WAAW,EAAER,UADuB;IAEpCY,YAAY,EAAE,CACZ,cADY,EAEX,IAAGZ,UAAW,IAFH,EAGX,IAAGA,UAAW,SAHH,EAIX,IAAGA,UAAW,SAJH,EAKX,IAAGA,UAAW,QALH,EAMX,IAAGA,UAAW,OANH,EAOX,IAAGA,UAAW,QAPH,EAQX,WAAUA,UAAW,IARV,EASX,WAAUA,UAAW,SATV,EAUX,WAAUA,UAAW,SAVV,EAWX,WAAUA,UAAW,QAXV,EAYX,WAAUA,UAAW,OAZV,EAaX,WAAUA,UAAW,QAbV,EAcX,GAAEA,UAAW,YAdF,EAeX,GAAEA,UAAW,aAfF,CAFsB;IAmBpCa,uBAAuB,EAAE,IAnBW;IAoBpCJ,OAAO,EAAEkC,WAAA,CAAGC,OAAH,EApB2B;IAqBpC5B,KAAK,EAAE,IArB6B;IAsBpCD,SAAS,EAAEnB,QAtByB;IAuBpCP,OAAO,EAAEK,kBAvB2B;IAwBpCuB,kBAAkB,EAAE;EAxBgB,CAAtC;EA2BA,MAAMS,iBAAsC,GAAG,EAC7C,GAAGc,QAD0C;IAE7C,GAAGnC,OAF0C;IAG7ChB,OAAO,EAAE,EACP,GAAGmD,QAAQ,CAACnD,OADL;MAEP,GAAGgB,OAAO,CAAChB;IAFJ;EAHoC,CAA/C;EASA,OAAOqC,iBAAP;AACD"}