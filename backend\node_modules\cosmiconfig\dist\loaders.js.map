{"version": 3, "file": "loaders.js", "names": ["importFresh", "loadJsSync", "filepath", "undefined", "require", "result", "loadJs", "href", "pathToFileURL", "default", "error", "parseJson", "loadJson", "content", "message", "yaml", "loadYaml", "load", "loaders"], "sources": ["../src/loaders.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-require-imports */\n\nimport { pathToFileURL } from 'url';\nimport { Loader, LoaderSync } from './index';\nimport { Loaders } from './types';\n\nlet importFresh: typeof import('import-fresh');\nconst loadJsSync: LoaderSync = function loadJsSync(filepath) {\n  if (importFresh === undefined) {\n    importFresh = require('import-fresh');\n  }\n\n  const result = importFresh(filepath);\n  return result;\n};\n\nconst loadJs: Loader = async function loadJs(filepath) {\n  try {\n    const { href } = pathToFileURL(filepath);\n    return (await import(href)).default;\n  } catch (error) {\n    return loadJsSync(filepath, '');\n  }\n};\n\nlet parseJson: typeof import('parse-json');\nconst loadJson: LoaderSync = function loadJson(filepath, content) {\n  if (parseJson === undefined) {\n    parseJson = require('parse-json');\n  }\n\n  try {\n    const result = parseJson(content);\n    return result;\n  } catch (error: any) {\n    error.message = `JSON Error in ${filepath}:\\n${error.message}`;\n    throw error;\n  }\n};\n\nlet yaml: typeof import('js-yaml');\nconst loadYaml: LoaderSync = function loadYaml(filepath, content) {\n  if (yaml === undefined) {\n    yaml = require('js-yaml');\n  }\n\n  try {\n    const result = yaml.load(content);\n    return result;\n  } catch (error: any) {\n    error.message = `YAML Error in ${filepath}:\\n${error.message}`;\n    throw error;\n  }\n};\n\nconst loaders: Loaders = { loadJs, loadJsSync, loadJson, loadYaml };\n\nexport { loaders };\n"], "mappings": ";;;;;;;AAEA;;AAFA;AAMA,IAAIA,WAAJ;;AACA,MAAMC,UAAsB,GAAG,SAASA,UAAT,CAAoBC,QAApB,EAA8B;EAC3D,IAAIF,WAAW,KAAKG,SAApB,EAA+B;IAC7BH,WAAW,GAAGI,OAAO,CAAC,cAAD,CAArB;EACD;;EAED,MAAMC,MAAM,GAAGL,WAAW,CAACE,QAAD,CAA1B;EACA,OAAOG,MAAP;AACD,CAPD;;AASA,MAAMC,MAAc,GAAG,eAAeA,MAAf,CAAsBJ,QAAtB,EAAgC;EACrD,IAAI;IACF,MAAM;MAAEK;IAAF,IAAW,IAAAC,kBAAA,EAAcN,QAAd,CAAjB;IACA,OAAO,CAAC,MAAM,OAAOK,IAAP,CAAP,EAAqBE,OAA5B;EACD,CAHD,CAGE,OAAOC,KAAP,EAAc;IACd,OAAOT,UAAU,CAACC,QAAD,EAAW,EAAX,CAAjB;EACD;AACF,CAPD;;AASA,IAAIS,SAAJ;;AACA,MAAMC,QAAoB,GAAG,SAASA,QAAT,CAAkBV,QAAlB,EAA4BW,OAA5B,EAAqC;EAChE,IAAIF,SAAS,KAAKR,SAAlB,EAA6B;IAC3BQ,SAAS,GAAGP,OAAO,CAAC,YAAD,CAAnB;EACD;;EAED,IAAI;IACF,MAAMC,MAAM,GAAGM,SAAS,CAACE,OAAD,CAAxB;IACA,OAAOR,MAAP;EACD,CAHD,CAGE,OAAOK,KAAP,EAAmB;IACnBA,KAAK,CAACI,OAAN,GAAiB,iBAAgBZ,QAAS,MAAKQ,KAAK,CAACI,OAAQ,EAA7D;IACA,MAAMJ,KAAN;EACD;AACF,CAZD;;AAcA,IAAIK,IAAJ;;AACA,MAAMC,QAAoB,GAAG,SAASA,QAAT,CAAkBd,QAAlB,EAA4BW,OAA5B,EAAqC;EAChE,IAAIE,IAAI,KAAKZ,SAAb,EAAwB;IACtBY,IAAI,GAAGX,OAAO,CAAC,SAAD,CAAd;EACD;;EAED,IAAI;IACF,MAAMC,MAAM,GAAGU,IAAI,CAACE,IAAL,CAAUJ,OAAV,CAAf;IACA,OAAOR,MAAP;EACD,CAHD,CAGE,OAAOK,KAAP,EAAmB;IACnBA,KAAK,CAACI,OAAN,GAAiB,iBAAgBZ,QAAS,MAAKQ,KAAK,CAACI,OAAQ,EAA7D;IACA,MAAMJ,KAAN;EACD;AACF,CAZD;;AAcA,MAAMQ,OAAgB,GAAG;EAAEZ,MAAF;EAAUL,UAAV;EAAsBW,QAAtB;EAAgCI;AAAhC,CAAzB"}