{"version": 3, "file": "types.js", "names": [], "sources": ["../src/types.ts"], "sourcesContent": ["import { Loader, LoaderSync, Options, OptionsSync } from './index';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type Config = any;\n\nexport type CosmiconfigResult = {\n  config: Config;\n  filepath: string;\n  isEmpty?: boolean;\n} | null;\n\nexport interface InternalOptions {\n  usePackagePropInConfigFiles?: boolean;\n  metaConfigFilePath: string | null;\n}\n\n// These are the user options with defaults applied, plus internal options possibly inferred from meta config\nexport interface ExplorerOptions extends Required<Options>, InternalOptions {}\nexport interface ExplorerOptionsSync\n  extends Required<OptionsSync>,\n    InternalOptions {}\n\nexport type Cache = Map<string, CosmiconfigResult>;\n\n// An object value represents a config object.\n// null represents that the loader did not find anything relevant.\n// undefined represents that the loader found something relevant\n// but it was empty.\nexport type LoadedFileContent = Config | null | undefined;\n\nexport interface Loaders {\n  [key: string]: Loader;\n}\n\nexport interface LoadersSync {\n  [key: string]: LoaderSync;\n}\n"], "mappings": ""}