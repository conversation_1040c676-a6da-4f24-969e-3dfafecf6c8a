{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../../../src/node/install.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,OAAO,EACP,OAAO,EACP,cAAc,EACd,oBAAoB,EACpB,qBAAqB,GACtB,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EAAC,mBAAmB,EAAC,MAAM,sCAAsC,CAAC;AAEzE,OAAO,EAAC,gBAAgB,EAAC,MAAM,wBAAwB,CAAC;AAExD;;GAEG;AACH,MAAM,iBAAiB,GAAG;IACxB,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,iBAAiB;CAClB,CAAC;AAEX;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe;IACnC,aAAa,EAAE,CAAC;IAEhB,MAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;IACzC,IAAI,aAAa,CAAC,YAAY,EAAE;QAC9B,WAAW,CAAC,mDAAmD,CAAC,CAAC;QACjE,OAAO;KACR;IAED,MAAM,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC;IAEtD,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;IACzC,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC3D;IAED,MAAM,OAAO,GAAG,aAAa,CAAC,cAAe,CAAC;IAC9C,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE1C,MAAM,iBAAiB,GACrB,aAAa,CAAC,eAAe,IAAI,mBAAmB,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC;IAE5E,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC;IAC3E,4DAA4D;IAC5D,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,IAAI,aAAa,CAAC,cAAe,CAAC;IAE7E,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC;YAC3B,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,wBAAwB,EAAE,oBAAoB,CAAC,OAAO,EAAE,OAAO,CAAC;YAChE,OAAO,EAAE,eAAe;SACzB,CAAC,CAAC;QAEH,WAAW,CACT,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,OAAO,mBAAmB,MAAM,CAAC,IAAI,EAAE,CACjF,CAAC;KACH;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CACX,2BAA2B,iBAAiB,CAAC,OAAO,CAAC,KAAK,OAAO,gEAAgE,CAClI,CAAC;QACF,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAiB;IACzC,QAAQ,OAAO,EAAE;QACf,KAAK,QAAQ;YACX,OAAO,OAAO,CAAC,MAAM,CAAC;QACxB,KAAK,SAAS;YACZ,OAAO,OAAO,CAAC,OAAO,CAAC;KAC1B;IACD,OAAO,OAAO,CAAC,MAAM,CAAC;AACxB,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,UAAmB;IACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;IAC1D,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAE3E,sCAAsC;IACtC,IAAI,CAAC,eAAe,EAAE;QACpB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;KACzB;AACH,CAAC;AAED;;GAEG;AACH,SAAS,aAAa;IACpB,8EAA8E;IAC9E,MAAM,eAAe,GACnB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC3E,MAAM,cAAc,GAClB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC1E,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAExD,IAAI,eAAe,EAAE;QACnB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,eAAe,CAAC;KAC9C;IACD,IAAI,cAAc,EAAE;QAClB,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;KAC5C;IACD,IAAI,YAAY,EAAE;QAChB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;KACxC;AACH,CAAC"}